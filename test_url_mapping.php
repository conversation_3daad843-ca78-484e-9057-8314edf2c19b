<?php
/**
 * URL映射功能测试文件
 * 测试URL提取和替换功能
 */

require_once 'vendor/autoload.php';

use App\Service\GroupAssistant\KnowledgeService;
use App\Param\Knowledge\KnowledgeParam;

class UrlMappingTest
{
    private $knowledgeService;

    public function __construct()
    {
        $this->knowledgeService = new KnowledgeService();
    }

    /**
     * 测试URL提取和替换功能
     */
    public function testUrlExtraction()
    {
        echo "=== URL映射功能测试 ===\n\n";

        // 测试用例1: 普通HTTP URL
        $testCase1 = "请查看这个文档：https://docs.example.com/api/guide 了解更多信息。";
        echo "测试用例1 - 普通URL:\n";
        echo "原文: $testCase1\n";
        $result1 = $this->testExtractAndReplace($testCase1);
        echo "处理后: {$result1['processed']}\n";
        echo "URL映射: " . json_encode($result1['url_map'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "还原后: {$result1['restored']}\n\n";

        // 测试用例2: Markdown链接格式
        $testCase2 = "参考文档：[API指南](https://api.example.com/docs) 和 [用户手册](https://help.example.com/manual)";
        echo "测试用例2 - Markdown链接:\n";
        echo "原文: $testCase2\n";
        $result2 = $this->testExtractAndReplace($testCase2);
        echo "处理后: {$result2['processed']}\n";
        echo "URL映射: " . json_encode($result2['url_map'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "还原后: {$result2['restored']}\n\n";

        // 测试用例3: 飞书文档链接
        $testCase3 = "查看飞书文档：https://bytedance.feishu.cn/docs/doccnAbCdEfGhIjKlMnOpQrStUv 获取详细信息。";
        echo "测试用例3 - 飞书文档链接:\n";
        echo "原文: $testCase3\n";
        $result3 = $this->testExtractAndReplace($testCase3);
        echo "处理后: {$result3['processed']}\n";
        echo "URL映射: " . json_encode($result3['url_map'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "还原后: {$result3['restored']}\n\n";

        // 测试用例4: 混合格式
        $testCase4 = "相关资料：[项目文档](https://project.example.com/docs) 和直接链接 https://github.com/example/repo 以及飞书文档 https://company.feishu.cn/wiki/wikcnAbCdEfGhIjKlMnOpQrStUv";
        echo "测试用例4 - 混合格式:\n";
        echo "原文: $testCase4\n";
        $result4 = $this->testExtractAndReplace($testCase4);
        echo "处理后: {$result4['processed']}\n";
        echo "URL映射: " . json_encode($result4['url_map'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "还原后: {$result4['restored']}\n\n";
    }

    /**
     * 测试URL提取和替换的完整流程
     */
    private function testExtractAndReplace($content)
    {
        $url_map = [];
        $url_index = 1;

        // 使用反射调用私有方法进行测试
        $reflection = new ReflectionClass($this->knowledgeService);
        $extractMethod = $reflection->getMethod('extractAndReplaceUrls');
        $extractMethod->setAccessible(true);
        
        $replaceMethod = $reflection->getMethod('replaceUrlMappings');
        $replaceMethod->setAccessible(true);

        // 提取并替换URL
        $processed = $extractMethod->invoke($this->knowledgeService, $content, $url_map, $url_index);
        
        // 模拟大模型回答（保持URL_X格式）
        $aiResponse = $processed;
        
        // 还原URL
        $restored = $replaceMethod->invoke($this->knowledgeService, $aiResponse, $url_map);

        return [
            'processed' => $processed,
            'url_map' => $url_map,
            'restored' => $restored
        ];
    }

    /**
     * 测试边界情况
     */
    public function testEdgeCases()
    {
        echo "=== 边界情况测试 ===\n\n";

        // 测试用例1: 无URL的文本
        $testCase1 = "这是一段普通的文本，没有任何链接。";
        echo "测试用例1 - 无URL文本:\n";
        echo "原文: $testCase1\n";
        $result1 = $this->testExtractAndReplace($testCase1);
        echo "处理后: {$result1['processed']}\n";
        echo "URL映射: " . json_encode($result1['url_map'], JSON_UNESCAPED_UNICODE) . "\n\n";

        // 测试用例2: 空字符串
        $testCase2 = "";
        echo "测试用例2 - 空字符串:\n";
        echo "原文: '$testCase2'\n";
        $result2 = $this->testExtractAndReplace($testCase2);
        echo "处理后: '{$result2['processed']}'\n";
        echo "URL映射: " . json_encode($result2['url_map'], JSON_UNESCAPED_UNICODE) . "\n\n";

        // 测试用例3: 重复URL
        $testCase3 = "链接1：https://example.com 和链接2：https://example.com 是相同的。";
        echo "测试用例3 - 重复URL:\n";
        echo "原文: $testCase3\n";
        $result3 = $this->testExtractAndReplace($testCase3);
        echo "处理后: {$result3['processed']}\n";
        echo "URL映射: " . json_encode($result3['url_map'], JSON_UNESCAPED_UNICODE) . "\n";
        echo "还原后: {$result3['restored']}\n\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new UrlMappingTest();
    $test->testUrlExtraction();
    $test->testEdgeCases();
    echo "测试完成！\n";
}
