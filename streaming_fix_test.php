<?php
/**
 * 测试修复后的流式输出URL映射逻辑
 */

class StreamingFixTest
{
    /**
     * 模拟URL替换方法
     */
    public function replaceUrlMappings($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            // 处理Markdown链接格式的替换
            if (!empty($url_info['text'])) {
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
            
            // 处理普通URL替换
            $content = str_replace($replacement_key, $url_info['url'], $content);
        }

        return $content;
    }

    /**
     * 处理Markdown链接格式的流式替换
     */
    public function processMarkdownLinks($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            if (!empty($url_info['text'])) {
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    /**
     * 测试修复后的流式输出逻辑
     */
    public function testFixedStreamingLogic()
    {
        echo "=== 修复后的流式输出测试 ===\n\n";

        // URL映射表
        $url_map = [
            'URL_1' => ['url' => 'https://api.example.com/docs', 'text' => 'API文档', 'original' => '[API文档](https://api.example.com/docs)'],
            'URL_2' => ['url' => 'https://github.com/company/project', 'text' => '', 'original' => 'https://github.com/company/project']
        ];

        echo "URL映射表：\n";
        foreach ($url_map as $key => $info) {
            echo "  $key => {$info['url']}" . (!empty($info['text']) ? " (文本: {$info['text']})" : "") . "\n";
        }
        echo "\n";

        // 测试场景1：URL标识被分割的情况
        echo "=== 场景1：URL标识被分割 ===\n";
        $chunks1 = [
            "请查看文档：UR",
            "L_1 了解详情，",
            "项目地址：URL_",
            "2"
        ];
        $this->simulateStreamingWithFinalReplacement($chunks1, $url_map, "URL标识被分割");

        // 测试场景2：Markdown链接被分割
        echo "\n=== 场景2：Markdown链接被分割 ===\n";
        $chunks2 = [
            "请参考 [API",
            "文档](URL_",
            "1) 获取信息"
        ];
        $this->simulateStreamingWithFinalReplacement($chunks2, $url_map, "Markdown链接被分割");

        // 测试场景3：正常情况
        echo "\n=== 场景3：正常完整输出 ===\n";
        $chunks3 = [
            "请查看 URL_1 和 ",
            "URL_2 获取更多信息"
        ];
        $this->simulateStreamingWithFinalReplacement($chunks3, $url_map, "正常完整输出");
    }

    /**
     * 模拟修复后的流式输出逻辑（最终替换策略）
     */
    private function simulateStreamingWithFinalReplacement($chunks, $url_map, $scenario)
    {
        echo "场景：$scenario\n";
        echo "输入chunks：" . json_encode($chunks, JSON_UNESCAPED_UNICODE) . "\n\n";

        $push_content = '';
        $main_content_buffer = '';
        $start_timestamp = microtime(true) * 1000;
        $output_count = 0;

        foreach ($chunks as $i => $chunk) {
            $push_content .= $chunk;
            $main_content_buffer .= $chunk;
            
            $now = microtime(true) * 1000;
            
            // 模拟300毫秒输出逻辑（这里简化为每2个chunk输出一次）
            if (($i + 1) % 2 == 0 || $i == count($chunks) - 1) {
                $output_count++;
                
                // 关键：对完整的累积内容进行URL替换
                $content_with_urls = $this->replaceUrlMappings($push_content, $url_map);
                $content_with_urls = $this->processMarkdownLinks($content_with_urls, $url_map);
                
                echo "输出 $output_count:\n";
                echo "  累积原始内容: '$push_content'\n";
                echo "  替换后内容: '$content_with_urls'\n";
                echo "  说明: " . $this->analyzeOutput($push_content, $content_with_urls) . "\n\n";
                
                // 清空缓冲区（模拟）
                $main_content_buffer = '';
                $start_timestamp = microtime(true) * 1000;
            }
        }
    }

    /**
     * 分析输出结果
     */
    private function analyzeOutput($original, $processed)
    {
        if ($original === $processed) {
            return "无URL需要替换";
        }
        
        $url_count = substr_count($original, 'URL_');
        $replaced_count = substr_count($processed, 'https://');
        
        if ($url_count > 0 && $replaced_count > 0) {
            return "成功替换了URL标识，即使它们在流式输出中被分割";
        } elseif ($url_count > 0 && $replaced_count == 0) {
            return "存在URL标识但未被替换（可能是不完整的标识）";
        } else {
            return "内容已正确处理";
        }
    }

    /**
     * 测试提示词改进效果
     */
    public function testPromptImprovement()
    {
        echo "=== 提示词改进效果测试 ===\n\n";

        echo "改进前的问题：\n";
        echo "- 大模型看到 URL_1 可能不知道这是文档链接\n";
        echo "- 可能会忽略这些看起来像占位符的内容\n\n";

        echo "改进后的提示词说明：\n";
        echo "- 明确告知 URL_1、URL_2 等代表真实的网页链接\n";
        echo "- 强调这些都是有效的文档资源\n";
        echo "- 指导如何正确引用这些URL标识\n\n";

        echo "预期效果：\n";
        echo "✅ 大模型会将 URL_1 理解为有效的文档链接\n";
        echo "✅ 会在回答中正确引用这些URL标识\n";
        echo "✅ 不会因为格式特殊而忽略这些重要资源\n\n";
    }

    /**
     * 对比新旧方案
     */
    public function compareApproaches()
    {
        echo "=== 新旧方案对比 ===\n\n";

        echo "旧方案问题：\n";
        echo "❌ 每个chunk立即替换，可能处理不完整的URL标识\n";
        echo "❌ 'URL_' + '1' 分别处理会导致替换失败\n";
        echo "❌ 性能问题：重复处理相同内容\n\n";

        echo "新方案优势：\n";
        echo "✅ 最终替换策略：确保URL标识完整性\n";
        echo "✅ 处理分割情况：即使URL_1被分成多个chunk也能正确替换\n";
        echo "✅ 性能优化：避免重复替换\n";
        echo "✅ 逻辑清晰：保持原有流式输出逻辑不变\n\n";

        echo "核心改进：\n";
        echo "1. 提示词优化：明确说明URL_X格式的含义\n";
        echo "2. 替换时机：从实时替换改为累积后替换\n";
        echo "3. 完整性保证：确保URL标识不会因分割而丢失\n\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new StreamingFixTest();
    $test->testFixedStreamingLogic();
    $test->testPromptImprovement();
    $test->compareApproaches();
    echo "测试完成！\n";
} else {
    header('Content-Type: text/plain; charset=utf-8');
    $test = new StreamingFixTest();
    $test->testFixedStreamingLogic();
    $test->testPromptImprovement();
    $test->compareApproaches();
    echo "测试完成！\n";
}
