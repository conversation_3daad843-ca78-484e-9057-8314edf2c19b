<?php
/**
 * 测试URL提取修复效果
 */

class UrlExtractionFixTest
{
    /**
     * 模拟sanitizeUrl方法
     */
    public function sanitizeUrl(string $url): string
    {
        // 去掉两端空白（包含中文空格、全角空格）
        $url = trim($url);
        $url = preg_replace('/^[\x{3000}\s]+|[\x{3000}\s]+$/u', '', $url);

        // 特殊处理：如果URL中包含JSON片段，截取到第一个JSON分隔符
        if (strpos($url, '",') !== false) {
            $url = substr($url, 0, strpos($url, '",'));
        }
        if (strpos($url, '\"') !== false) {
            $url = substr($url, 0, strpos($url, '\"'));
        }
        if (strpos($url, '},{') !== false) {
            $url = substr($url, 0, strpos($url, '},{'));
        }

        // 去掉URL末尾多余的标点符号或引号
        $url = rtrim($url, ")]}>\"',\\");

        // 如果包含XML/HTML标签，去掉所有标签
        $url = strip_tags($url);

        // 去掉末尾意外拼接的XML片段，如 </msg>、</chat>
        $url = preg_replace('/<\/?[a-zA-Z][^>]*>$/', '', $url);

        // 额外防护：如果URL中存在换行或制表符，移除
        $url = str_replace(["\n", "\r", "\t"], '', $url);

        // 验证URL格式，如果不是有效URL则返回空字符串
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return '';
        }

        return $url;
    }

    /**
     * 模拟extractAndReplaceUrls方法
     */
    public function extractAndReplaceUrls(string $message_content, array &$url_map, int &$url_index)
    {
        $url_patterns = [
            // Markdown链接格式 [text](url) 优先处理，避免被普通URL规则抢先替换
            '/\[([^\]]*)\]\((https?:\/\/[^\s\[\]()\"\'\\,}]+)\)/i',
            // HTTP/HTTPS URLs - 改进边界识别，排除JSON分隔符和引号
            '/https?:\/\/[^\s\[\]()\"\'\\,}]+/i',
            // 飞书文档链接等特殊格式（可能不带协议）
            '/(?:https?:\/\/)?[a-zA-Z0-9][\w\-]*\.(?:feishu|lark)\.cn\/[^\s\[\]()\"\'\\,}]+/i'
        ];

        foreach ($url_patterns as $pattern) {
            $message_content = preg_replace_callback($pattern, function ($matches) use (&$url_map, &$url_index) {
                $full_match = $matches[0];
                $url = '';
                $text = '';

                // 处理Markdown链接格式
                if (isset($matches[2])) {
                    $text = $matches[1];
                    $url = $matches[2];
                    $url = $this->sanitizeUrl($url);
                    if (empty($url)) return $full_match; // 如果URL无效，保持原样
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => $text, 'original' => $full_match];
                    $url_index++;
                    return "[{$text}]({$replacement_key})";
                } else {
                    // 处理普通URL
                    $url = $full_match;
                    $url = $this->sanitizeUrl($url);
                    if (empty($url)) return $full_match; // 如果URL无效，保持原样
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => '', 'original' => $full_match];
                    $url_index++;
                    return $replacement_key;
                }
            }, $message_content);
        }

        return $message_content;
    }

    /**
     * 测试修复效果
     */
    public function testUrlExtractionFix()
    {
        echo "=== URL提取修复效果测试 ===\n\n";

        // 测试用例1：您遇到的问题数据
        $problematic_content = 'https://lx3qcyzne8.feishu.cn/wiki/GafjwtDsxi2sXmkYipecdzLxn0c","text":"https://lx3qcyzne8.feishu.cn/wiki/GafjwtDsxi2sXmkYipecdzLxn0c"},{"tag":"text","text":"\\n游戏包："},{"tag":"a","href":"https://lx3qcyzne8.feishu.cn/file/TwyHb3WdDo8myRxZw8XcT8jznqW","text":"Apk_0.4.84.apk.1.1.apk.1.1.apk';

        echo "测试用例1 - 问题数据:\n";
        echo "原始内容: $problematic_content\n\n";

        $url_map = [];
        $url_index = 1;
        $processed = $this->extractAndReplaceUrls($problematic_content, $url_map, $url_index);

        echo "处理后内容: $processed\n";
        echo "提取的URL映射:\n";
        foreach ($url_map as $key => $info) {
            echo "  $key => {$info['url']}\n";
        }
        echo "\n";

        // 测试用例2：正常的URL
        echo "测试用例2 - 正常URL:\n";
        $normal_content = '请查看文档：https://lx3qcyzne8.feishu.cn/wiki/GafjwtDsxi2sXmkYipecdzLxn0c 了解详情';
        echo "原始内容: $normal_content\n";

        $url_map2 = [];
        $url_index2 = 1;
        $processed2 = $this->extractAndReplaceUrls($normal_content, $url_map2, $url_index2);

        echo "处理后内容: $processed2\n";
        echo "提取的URL映射:\n";
        foreach ($url_map2 as $key => $info) {
            echo "  $key => {$info['url']}\n";
        }
        echo "\n";

        // 测试用例3：Markdown链接格式
        echo "测试用例3 - Markdown链接:\n";
        $markdown_content = '参考[文档](https://lx3qcyzne8.feishu.cn/docx/HBMndnK0yomnL1xjhUWcQnIPnDN","text":"‌‍‌﻿‌﻿‍‌‍﻿⁠‌‬‍‌‍‍‬﻿‌﻿⁠)了解更多';
        echo "原始内容: $markdown_content\n";

        $url_map3 = [];
        $url_index3 = 1;
        $processed3 = $this->extractAndReplaceUrls($markdown_content, $url_map3, $url_index3);

        echo "处理后内容: $processed3\n";
        echo "提取的URL映射:\n";
        foreach ($url_map3 as $key => $info) {
            echo "  $key => {$info['url']}" . (!empty($info['text']) ? " (文本: {$info['text']})" : "") . "\n";
        }
        echo "\n";
    }

    /**
     * 测试sanitizeUrl方法
     */
    public function testSanitizeUrl()
    {
        echo "=== sanitizeUrl方法测试 ===\n\n";

        $test_cases = [
            'https://lx3qcyzne8.feishu.cn/wiki/GafjwtDsxi2sXmkYipecdzLxn0c","text":"https://lx3qcyzne8.feishu.cn/wiki/GafjwtDsxi2sXmkYipecdzLxn0c"},{"tag":"text"',
            'https://lx3qcyzne8.feishu.cn/docx/HBMndnK0yomnL1xjhUWcQnIPnDN","text":"‌‍‌﻿‌﻿‍‌‍﻿⁠‌‬‍‌‍‍‬﻿‌﻿⁠',
            'https://example.com/path?param=value',
            'https://example.com/path</msg>',
            'https://example.com/path"',
            'https://example.com/path)',
            'invalid-url-format',
            'https://example.com/path},{other:data'
        ];

        foreach ($test_cases as $i => $test_url) {
            $cleaned = $this->sanitizeUrl($test_url);
            echo "测试 " . ($i + 1) . ":\n";
            echo "  原始: $test_url\n";
            echo "  清洗后: " . ($cleaned ?: '(无效URL)') . "\n";
            echo "  状态: " . ($cleaned ? '✅ 有效' : '❌ 无效') . "\n\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new UrlExtractionFixTest();
    $test->testUrlExtractionFix();
    $test->testSanitizeUrl();
    echo "测试完成！\n";
} else {
    header('Content-Type: text/plain; charset=utf-8');
    $test = new UrlExtractionFixTest();
    $test->testUrlExtractionFix();
    $test->testSanitizeUrl();
    echo "测试完成！\n";
}
