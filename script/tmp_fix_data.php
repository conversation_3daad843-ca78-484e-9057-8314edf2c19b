<?php


use App\Model\HttpModel\Feishu\Aily\ChatModel;
use App\MysqlConnection;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');


// 设置MySQL连接对象
MysqlConnection::setConnection();
$chat_model = new ChatModel();
$redis = \App\Struct\RedisCache::getInstance();
\App\ElasticsearchConnection::setConnection();



//$service = new \App\Service\DataBot\AilyService();
//$access_token = '';
$union_id = '151';
//$session_id = $service->getSessionID($union_id);

//$res = getRequires(151, 'session_4fvgafz47p830');
//$msg_id_list = getMsgList('session_4fvgafz47p830', 'run_4fvganhktp0b6');
//

//// 获取消息
//$message_res = $chat_model->listMessage('session_4fvg1ewb1ry4j', 'run_4fvg1j5kmcpt9');
//var_dump($message_res);
//exit;

try {

////    // 1. 创建会话
    $session_data = $chat_model->createSession($union_id);
    $session_id = $session_data['data']['session']['id'];
//    $session_id = 'session_4fvj701bydayt';
    echo "会话创建成功,session_id是$session_id", PHP_EOL;

    // 2. 创建用户问题消息
    $message_data = $chat_model->createMessage($session_id, "https://lx3qcyzne8.feishu.cn/wiki/GNnswhSkAinIypk6Kkbc3OK3nTd?fromScene=spaceOverview");

    echo "问题消息创建成功", PHP_EOL;
    // 3.创建运行 // 判断需不需要获取原来的运行
    $run_info = getRequires($union_id, $session_id);
    if ($run_info) {
        $run_id = $run_info['run_id'];
    } else {
        $run_data = $chat_model->createRun($session_id, 'skill_e22317e8c39c');
        $run_id = $run_data['data']['run']['id'];
    }


    echo "运行创建成功,run_id是$run_id", PHP_EOL;
    // 4. 轮询
    $startTime = time();
    for ($i = 0; $i < 300; $i++) {
        usleep(500000); // 0.5秒
        $status_res = $chat_model->getRun($session_id, $run_id);
        $run_status = $status_res['data']['run']['status'] ?? null;

        var_dump($run_status);
        if ($run_status === 'IN_PROGRESS') {
            // 获取消息
            $message_res = $chat_model->listMessage($session_id, $run_id);

            $message_list = $message_res['data']['messages'] ?? [];

            $content = [];
            foreach ($message_list as $msg) {
                $send_type = $msg['sender']['sender_type'] ?? '';
                var_dump($msg);
//                if ($send_type === 'ASSISTANT') {
//                    // 机器人消息才需要输出 // TODO 倒是要换成更新卡片的逻辑
//                    // 判断一下是否已经发送过这个消息id,还要判断一下COMPLETED 存入msg_id list
//                    $msg_id_list = getMsgList($session_id, $run_id);
//                    if (!in_array($msg['id'], $msg_id_list)) {
//                        var_dump($run_status);
//                        var_dump($msg['content']);
//                    }
//                    if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
//                        saveMsgList($session_id, $run_id, $msg['id']);
//                    }
//
//                }
            }


        } elseif ($run_status === 'COMPLETED') {
            // 获取消息
            $message_res = $chat_model->listMessage($session_id, $run_id);
            $message_list = $message_res['data']['messages'] ?? [];

            $content = [];
            foreach ($message_list as $msg) {
                $send_type = $msg['sender']['sender_type'] ?? '';
                var_dump($msg);
//                if ($send_type === 'ASSISTANT') {
//                    // 判断一下是否已经发送过这个消息id,还要判断一下COMPLETED 存入msg_id list
//                    $msg_id_list = getMsgList($session_id, $run_id);
//                    if (!in_array($msg['id'], $msg_id_list)) {
//                        var_dump($run_status);
//                        var_dump($msg['content']);
//                    }
//                    if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
//                        saveMsgList($session_id, $run_id, $msg['id']);
//                    }
//                }
            }

            deleteRequires($union_id, $session_id);

            break;
        } elseif ($run_status === 'REQUIRES_MESSAGE') {
            // 获取消息
            $message_res = $chat_model->listMessage($session_id, $run_id);
            $message_list = $message_res['data']['messages'] ?? [];
            foreach ($message_list as $msg) {
                $send_type = $msg['sender']['sender_type'] ?? '';
                var_dump($msg);
//                if ($send_type === 'ASSISTANT') {
//                    // 判断一下是否已经发送过这个消息id,还要判断一下COMPLETED 存入msg_id list
//                    $msg_id_list = getMsgList($session_id, $run_id);
//                    if (!in_array($msg['id'], $msg_id_list)) {
//                        var_dump($run_status);
//                        var_dump($msg['content']);
//                    }
//                    if ($msg['status'] === 'COMPLETED' && !in_array($msg['id'], $msg_id_list)) {
//                        saveMsgList($session_id, $run_id, $msg['id']);
//                    }
//                }
            }
            // 需要把session_id还有run_id都存起来
            saveRequires($union_id, $session_id, $run_id);
            break;
        } elseif ($run_status === 'FAILED') {
            // 失败状态
            $message_res = $chat_model->listMessage($session_id, $run_id);
            // 获取消息
            $message_res = $chat_model->listMessage($session_id, $run_id);
            $message_list = $message_res['data']['messages'] ?? [];
            foreach ($message_list as $msg) {
                var_dump($msg);
            }
            break;
        }
    }


    echo PHP_EOL . "执行完成" . PHP_EOL;
} catch (\Throwable $e) {
    echo $e->getMessage();
    echo PHP_EOL;
    echo $e->getTraceAsString();
}


function saveMsgList($session_id, $run_id, $msg_id)
{
    $msg_id_list = getMsgList($session_id, $run_id);
    $msg_id_list[] = $msg_id;

    $redis = \App\Struct\RedisCache::getInstance();
    $key = $session_id . '_' . $run_id;
    $redis->setex($key, 7200, serialize($msg_id_list));
}

function getMsgList($session_id, $run_id)
{
    $redis = \App\Struct\RedisCache::getInstance();
    $key = $session_id . '_' . $run_id;
    $res = $redis->get($key);
    if ($res) {
        $ret = unserialize($res);
    } else {
        $ret = [];
    }
    return $ret;
}

function saveRequires($union_id, $session_id, $run_id)
{
    $redis = \App\Struct\RedisCache::getInstance();
    $key = $union_id . $session_id;
    $redis->set($key, serialize([
        'union_id'   => $union_id,
        'run_id'     => $run_id,
        'session_id' => $session_id,
    ]));
}

function getRequires($union_id, $session_id)
{
    $redis = \App\Struct\RedisCache::getInstance();
    $key = $union_id . $session_id;
    $res = $redis->get($key);
    if ($res) {
        $ret = unserialize($res);
    } else {
        $ret = [];
    }
    return $ret;
}

function deleteRequires($union_id, $session_id)
{
    $redis = \App\Struct\RedisCache::getInstance();
    $key = $union_id . $session_id;
    $redis->del($key);
}

////$logger = Helpers::getLogger('tmp_fix_data');
//
////$logger->info('开始获取数据');
//
////$old_sql = "select * FROM rank_permission_all_1 where type IN (1,5,6,7,9)";
////$old_list = MysqlConnection::getConnection()->select($old_sql);
////
////
////$logger->info('数据获取完成，开始replace');
////$replace_data = [];
////foreach ($old_list as $item) {
////    $replace_data[] = [
////        'level' => $item->level,
////        'rank_id' => $item->rank_id,
////        'type' => $item->type,
////        'platform' => $item->platform
////    ];
////}
////
////$all_model = new RankPermissionAllModel();
////$all_model->replace($replace_data);
////
////
////$logger->info('replace完成，开始删除多余数据');
////
////$new_list = $all_model->getListByRank();
////
////foreach ($new_list as $item) {
////    $logger->info('处理：', [$item->level, $item->rank_id, $item->platform]);
////    $type_list = $all_model->getListByRankPlatform($item->level, $item->rank_id, $item->platform);
////
////    // 说明type不止有1 还有其他游戏类型全选，要删除多余的记录
////    if ($type_list->where('type', '=', 1)->isNotEmpty() && $type_list->count() > 1) {
////        $all_model->deleteRankType($item->rank_id, $item->level, $item->platform);
////        $logger->info('删除多余记录', [$type_list]);
////    }
////
////
////    $logger->info('处理完成：', [$item->level, $item->rank_id, $item->platform]);
////}
//$now = time();
//
//$model = new RankRoutePermissionModel();
//// 投放总览有留存指标的，都开下付费留存的指标
//
////// 付费留存的指标id
////$route_permission_id_list = [
////    8927,
////    8926,
////    8925,
////    8924,
////    8923,
////    8922,
////    8921,
////    8920,
////    8919,
////    8918,
////    8917,
////    8916,
////];
//
//$map = [
//    75  => [8918, 8917, 8916],
//    10  => [8924, 8923, 8922],
//    192 => [8921, 8920, 8919],
//    237 => [8927, 8926, 8925],
//];
//
//foreach ($map as $route_permission_id => $route_permission_id_list) {
//
//
//    // 投放总览有留存指标的rank level
//    $sql = "SELECT DISTINCT `rank_id`,`level` from rank_route_permission where route_permission_id  = {$route_permission_id}";
//    $list = MysqlConnection::getConnection()->select($sql);
//    foreach ($list as $item) {
//        $insert_data = [];
//
//        foreach ($route_permission_id_list as $list_id) {
//            $insert_data[] = [
//                'level'               => $item->level,
//                'rank_id'             => $item->rank_id,
//                'route_permission_id' => $list_id,
//                'create_time'         => $now,
//                'update_time'         => $now,
//            ];
//
//        }
//
//        $model->replace($insert_data);
//        echo "level: {$item->level},rank_id:{$item->rank_id}" . ' 处理成功', PHP_EOL;
//    }
//}
//
//echo "任务完成", PHP_EOL;

//$data = [
//    'start_date' => '2025-05-12 00:00:00',
//    'end_date'   => '2025-05-13 23:59:59',
//    'type'       => [3, 4, 5],
//    'rows'       => 10000,
//    'page'       => 1,
//    'route_list' => ['/dms/twdblog/cost-input']
//];
//$param = new LogSearchParam($data);
//$logic = new LogEsLogic();
//$list = $logic->getList($param);
//usort($list, function ($a, $b) {
//    return strtotime($a['request_time']) <=> strtotime($b['request_time']);
//});
//
//$cost_input_logic = new \App\Logic\DMS\CostInputLogic();
//
//
//$logger = Helpers::getLogger('cost_tmp');
//foreach ($list as $item) {
//    $request_message = json_decode($item['request_message'], true);
//    $input = new \App\Struct\Input();
//    $input->setDataByArray($request_message);
//    switch ($item['api']) {
//        case "/dms/costInput/deleteCostInput":
//            $logger->info('处理删除消耗', ['request_message' => $request_message]);
//            try {
//                $cost_input_logic->deleteCostInputTmp($input);
//            } catch (\Throwable $exception) {
//                $logger->error($exception->getMessage());
//            }
//
//            break;
//        case "/dms/costInput/editCostInput":
//            $logger->info('处理编辑消耗', ['request_message' => $request_message]);
//            try {
//                $cost_input_logic->editCostInputTmp($input);
//            } catch (\Throwable $exception) {
//                $logger->error($exception->getMessage());
//            }
//            break;
//        case "/dms/costInput/costInput":
//            $logger->info('处理新增消耗', ['request_message' => $request_message]);
//            try {
//                $cost_input_logic->costInputTmp($input);
//            } catch (\Throwable $exception) {
//                $logger->error($exception->getMessage());
//            }
//            break;
//        default:
//            break;
//    }
//}
//exit;
//
//var_dump($list);
//
//exit;