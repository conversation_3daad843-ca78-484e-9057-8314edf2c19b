<?php

use App\Logic\DSP\DimensionMonitorRobotLogic;
use App\Model\RedisModel\DimensionMonitorRobotFrequencyModel;
use App\Model\SqlModel\Zeda\DimensionMonitorRobotLogModel;
use App\Model\SqlModel\Zeda\DimensionMonitorRobotModel;
use App\MysqlConnection;
use App\Param\ADServing\ADIntelligentMonitorBindParam;
use App\Param\ADServing\ADIntelligentMonitorRobotLogParam;
use App\Param\ADServing\DimensionMonitorRobotLogParam;
use App\Service\MediaIntelligentMonitor\MediaIntelligentMonitor;
use Illuminate\Support\Collection;

require dirname(__DIR__) . '../../common/init.php';
date_default_timezone_set('PRC');
MysqlConnection::setConnection();

echo date('Y-m-d H:i:s') . ' 维度监控开始' . PHP_EOL;

$redis_model = (new DimensionMonitorRobotFrequencyModel());

$list = (new DimensionMonitorRobotModel())->getExecList();

foreach ($list as $robot) {
    if ($redis_model->getFrequencyLockForRobot($robot->id)) {
        continue;
    }
    try {
        $robot->filter = json_decode($robot->filter, true);
        $robot->calc = json_decode($robot->calc, true);
        $robot->target = json_decode($robot->target, true);
        $robot->order = json_decode($robot->order, true);

        $input = [
            'media_type' => $robot->media_type, // 媒体类型
            'range_hour' => $robot->range_hour,
            'platform' => $robot->platform,
            'target' => array_map(function ($ele) {
                return $ele['column'];
            }, $robot->target['data']), // 自定义指标
            'filter' => array_map(function ($ele) {
                return [
                    'column' => $ele['column'],
                    'value' => $ele['value'],
                    'condition' => $ele['condition'],
                ];
            }, $robot->filter), // 内容筛选
            'calc' => array_map(function ($ele) {
                return [
                    'column' => $ele['name'],
                    'value' => $ele['value'],
                    'condition' => $ele['operator'],
                ];
            }, $robot->calc['calc']), // 数值条件筛选
            'order' => $robot->order, // 排序
            'condition' => $robot->calc['type'],
            'dimension_exec_target_dim' => $robot->exec_target_dim,
            'limit' => 0
        ];

        $monitor = MediaIntelligentMonitor::create($robot->media_type, $robot->media_agent_type);

        $result = (new DimensionMonitorRobotLogic())->previewDimensionMonitorRobot($input);

        /* @var Collection $list */
        $exec_list = $result['list'];
        if ($exec_list->isNotEmpty()) {
            foreach ($exec_list as $exec_info) {

                $bind = new ADIntelligentMonitorBindParam([
                    'media_type' => $robot->media_type,
                    'media_agent_type' => $robot->media_agent_type,
                    'bind_type' => 'precise',
                    'bind_target_type' => $robot->exec_target_dim,
                    'bind_target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
                    'target_type' => $robot->exec_target_dim,
                    'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
                    'unbind_target_value_list' => [],
                    'finish_target_value_list' => [],
                    'monitor_robot_id' => $robot->id,
                    'first_exec_time' => date('Y-m-d H:i:s'),
                ]);
                // {"target": "put_status", "target_value": "2"}
                $action = [
                    'target' => $robot->action_target,
                    'target_value' => $robot->action_value,
                ];


                $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}] = new ADIntelligentMonitorRobotLogParam([
                    'robot_id' => $robot->id,
                    'creator' => $robot->creator,
                    'media_type' => $robot->media_type,
                    'media_agent_type' => $robot->media_agent_type,
                    'bind_type' => 'precise',
                    'bind_target_type' => $robot->exec_target_dim,
                    'bind_target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
                    'target_type' => $robot->exec_target_dim,
                    'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
                ]);

                $monitor->execAction($bind, $action);

                $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->action_result = 'ok';
                if ($monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective == '') {
                    $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective = 'effective';

                    (new DimensionMonitorRobotLogModel())->log(new DimensionMonitorRobotLogParam([
                        'robot_id' => $robot->id,
                        'media_type' => $robot->media_type,
                        'media_agent_type' => $robot->media_agent_type,
                        'target_type' => $robot->exec_target_dim,
                        'target_value' => $exec_info->{$robot->exec_target_dim . '_id'},
                        'action_target' => $robot->action_target,
                        'action_value' => $robot->action_value,
                        'is_effective' => $monitor->log_param_map[$exec_info->{$robot->exec_target_dim . '_id'}]->is_effective,
                        'msg' => '',
                        'sql' => $result['sql'],
                        'creator' => $robot->creator,
                    ]));
                    exit;
                }
            }
        }

        $redis_model->setFrequencyLockForRobot($robot->id, $robot->monitoring_frequency);
    } catch (Throwable $e) {
        echo date('Y-m-d H:i:s 出错了:') . $e->getMessage() . PHP_EOL;
        continue;
    }
}

exit;
