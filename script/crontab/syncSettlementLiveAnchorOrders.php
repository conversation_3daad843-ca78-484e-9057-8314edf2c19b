<?php
/**
 * 拉取结算单广告位关联订单 - by 郭嘉辉
 * 意图: 从dwd_live_anchor_order_cost_calc拉取订单数据, 用来跟结算单的site_id关联查询
 * *\/5 * * * * php feishuTaskSync.php
 * @server 120.55.83.156 zx-dms
 */

use App\Model\SqlModel\DataMedia\DwdLiveAnchorOrderCostCalcModel;
use App\Model\SqlModel\Zeda\SiteSettlementLiveAnchorOrdersModel;
use App\MysqlConnection;
use App\Utils\Helpers;

require dirname(__DIR__) . '/../common/init.php';

date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
Helpers::getLogger('sync_settlement_order')->info('同步任务开始');
try {
    $data = (new DwdLiveAnchorOrderCostCalcModel)->getSiteIdOrders();
    $insert_data = [];
    $data->each(function ($item) use (&$insert_data) {
        $insert_data[] = [
            'platform'        => $item->platform,
            'site_id'         => $item->site_id,
            'first_live_date' => $item->first_live_date,
            'live_order_ids'  => $item->live_order_ids,
            'trade_nos'       => $item->trade_nos,
        ];
    });

    (new SiteSettlementLiveAnchorOrdersModel())->replace($insert_data);
    Helpers::getLogger('sync_settlement_order')->info('数据入库成功', ['count' => count($insert_data)]);
} catch (\Throwable $exception) {
    Helpers::getLogger('sync_settlement_order')->error('拉取结算单广告位关联订单失败', ['msg' => $exception->getMessage()]);
}

Helpers::getLogger('sync_settlement_order')->info('同步任务结束');