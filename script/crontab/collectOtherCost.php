<?php

/**
 * from 婷总
 * 需求内容：
 *  每天定时从 DMS-数据中心-运营利润的报表 按给定的某个维度取数据，数据包含 服务器费用/授权金/代言费等，然后写入datahub的v2_dwd_game_other_cost_log表和v2_dwd_game_cp_money_log
 * 0 12,14 * * * php collectOtherCost.php
 * @server ************ zx-script
 */

use App\Model\SqlModel\Tanwan\V2DwdGameCpMoneyLogModel;
use App\Model\SqlModel\Tanwan\V2DwdGameOtherCostLogModel;
use App\MysqlConnection;
use App\Param\DimProfitListParam;
use App\Logic\DMS\OperationProfitLogic;
use App\Utils\Math;


require dirname(__DIR__) . '/../common/init.php';
date_default_timezone_set('PRC');

MysqlConnection::setConnection();

// 每天跑全量数据, 2014年到四年后的日期，因为财务会超前录数据
$start_date = '2014-01-01'; // 数据最早的日期
$end_date = date('Y-m-t', strtotime("+4 year", strtotime(date('Y-m-01')))); // 基准时间设为每个月的1号。 解释：当基准日期为7.29号时，推N年前的数据，会有闰年一说，2023年2月只有28天，这样就导致少推一个月的数据
$monthly_dates = getMonthlyStartAndEndDates($start_date, $end_date);

foreach ($monthly_dates as $date_data) {
    echo "START SCRIPT:" . date('Y-m-d H:i:s') . PHP_EOL;
    // 其他成本表数据--------------------------------------------------------------------start
    $other_cost_param = [
        'start_time' => $date_data['month_start'],
        'end_time' => $date_data['month_end'],
        // 固定的维度： 平台, 子游戏
        // 再加一个维度：proxy_type
        'dimension' => [
            "platform",
            "game_id",
            "proxy_type"
        ],
        'dimension_filter' => [],
        // 固定聚合方式： 按月
        'aggregation_type' => "按月",
        'true_cost' => 0,
    ];
    echo $other_cost_param['start_time'] . '-' . $other_cost_param['end_time'] . PHP_EOL;

    $other_cost_param = new DimProfitListParam($other_cost_param);
    $other_cost_param->setUserPermission(261);// 超管账号：中旭未来
    $other_cost_data = (new OperationProfitLogic())->getDimList($other_cost_param, 4);

    $other_cost_insert_data = [];
    foreach ($other_cost_data['list'] as $item) {
        $other_cost_insert_data[] = [
            'platform' => $item->platform,
            'game_id' => $item->game_id,
            'endorsement_cost' => $item->endorsement_money ?? '0.00',
            'license_cost' => $item->authorization_money ?? '0.00',
            'server_money' => $item->server_cost ?? '0.00',
            'tdate' => $item->game_date . '-01',
            'proxy_type' => $item->proxy_type,
            'is_delete' => 0
        ];
    }
    (new V2DwdGameOtherCostLogModel())->deleteByTdate($date_data['month_start']);
    (new V2DwdGameOtherCostLogModel())->replace($other_cost_insert_data);

    // 其他成本表数据--------------------------------------------------------------------end

    // 分成表数据--------------------------------------------------------------------start
    $cp_money_param = [
        'start_time' => $date_data['month_start'],
        'end_time' => $date_data['month_end'],
        // 固定的维度： 平台, 子游戏, 买量平台
        'dimension' => [
            "platform",
            "game_id",
            "proxy_type"
        ],
        'dimension_filter' => [],
        // 固定聚合方式： 按月
        'aggregation_type' => "按月",
        'true_cost' => 0,
    ];

    $cp_money_param = new DimProfitListParam($cp_money_param);
    $cp_money_param->setUserPermission(261);// 超管账号：中旭未来
    $cp_money_data = (new OperationProfitLogic())->getDimList($cp_money_param, 6);

    $game_cp_money_data = [];
    foreach ($cp_money_data['list'] as $item) {
        if ($item->game_pay_money <= 0) continue;// 婷总要求无付费的数据不用入库

        $game_cp_money_data[] = [
            'platform' => $item->platform,
            'game_id' => $item->game_id,
            'tdate' => $item->game_date . '-01',
            'cp_money' => $item->profit ?? '0.00',
            'game_money' => $item->game_pay_money ?? '0.00',
            'cp_percent' => $item->per_profit ?? '0.00',
            'proxy_type' => $item->proxy_type,
            'is_delete' => 0
        ];

    }
    (new V2DwdGameCpMoneyLogModel())->deleteByTdate($date_data['month_start']);
    (new V2DwdGameCpMoneyLogModel())->replace($game_cp_money_data);
    // 分成表数据--------------------------------------------------------------------start
}


function getMonthlyStartAndEndDates($start_date, $end_date): array
{
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $end->modify('last day of this month'); // Move to the end of the month

    $result = [];

    while ($start <= $end) {
        $month_start = $start->format('Y-m-01');
        $month_end = $start->format('Y-m-t'); // 't' gives the last day of the month

        if (new DateTime($month_end) > $end) {
            $month_end = $end->format('Y-m-d');
        }

        $result[] = ['month_start' => $month_start, 'month_end' => $month_end];

        $start->modify('first day of next month');
    }

    return $result;
}
