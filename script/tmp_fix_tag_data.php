<?php
/**
 * 修复标签的问题
 */


use App\Model\HttpModel\Aliyun\DashScope\Embeddings\EmbeddingsModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\RoutePermissionModel;
use App\Model\HttpModel\Aliyun\DashVector\Collections\CollectionsModel;
use App\MysqlConnection;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageSummary;
use App\Utils\Helpers;

require dirname(__DIR__) . '/common/init.php';
date_default_timezone_set('PRC');

// 设置MySQL连接对象
MysqlConnection::setConnection();
//$logger = Helpers::getLogger('update_message');
//$logger->info('任务开始');
//$message_list = MysqlConnection::getConnection()->select('select * from feishu_message where (format_message like  "%.feishu.cn/%" or format_message like  "%.larkoffice.com/%") and state = 1');
//
//$message_id_list = [];
//foreach ($message_list as $message) {
//    $message_id_list[] = $message->message_id;
//}
//$message_id_list = ['om_x100b4c752a3be8240f3c24c6ef9063d','om_x100b4c7034cc24dc0f463fdbc44e091','om_x100b4c70e868c8800f3d3981944a9ba','om_x100b4c662cbe96fc0f327002ed819a8'];

//(new \App\Service\GroupAssistant\MessageFormat())->updateMessage($message_id_list);
//
//exit;
//
//(new MessageSummary())->run();
//
//exit;
//
//echo "任务结束", PHP_EOL;
//exit;
//
////
//$site_model = new \App\Model\SqlModel\Zeda\SiteSettlementModel();
//
//
//$ids = $site_model->getListTmp();
//(new \App\Logic\DMS\OuterLogic())->settleTmp($ids);



//
//$union_id = 'on_b8ef743bfe285ce29e8079b882739aa3';
//
//
////        // 获取历史session 的 message
//$session_key = KnowledgeService::QA_SESSION_KEY . $union_id;
////        RedisCache::getInstance()->del($session_key);
////        dd(1);
////        $list = (new KnowledgeService())->getSessionList($session_key);
//////
////        dd($list);
//$query = '仇俊文什么时候想要和我对接需求';
//
//(new KnowledgeService())->getKnowledgeAnswer($query, $union_id);
//echo PHP_EOL;
//exit;


//(new \App\Service\EnterpriseDiDiService())->syncAllUser();
//(new GroupAssistantService())->initUserChatInfo();
// 初始化群总结
//(new MessageSummary())->run();
// 初始化用户群信息
echo '任务完成', PHP_EOL;
exit;


$logger = \App\Utils\Helpers::getLogger('tmp_bot_message');
// 获取群列表数据
$auth_model = new AuthModel();

// 先获取access_token
$tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
    GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
// 获取到群聊天列表
$chat_model = new ChatsModel();

$chat_list = $chat_model->chatList($tenant_access_token);
$logger->info('获取到聊天群列表');
$service = new GroupAssistantService();

foreach ($chat_list as $item) {
    $chat_id = $item['chat_id'];

    if ($chat_id !== 'oc_c730808f049bfefa89cd7f931dc8bc14') {
        continue;
    }
    // 先获取access_token
    $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
        GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
    // 拉取历史消息
    // 先去获取聊天记录
    $start_time = strtotime(date("Y-m-d H:i:s", strtotime("-365 day")));
    $end_time = strtotime(date("Y-m-d H:i:s"));
    $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time, 50, 'ByCreateTimeAsc');

    $logger->info('获取到了消息历史，开始解析聊天记录');
    $message_model = new FeiShuMessageModel();

    // 解析聊天记录
    foreach ($message_list as $message) {
        if (!in_array($message['msg_type'], ['post', 'file', 'image'])) {
            // 这些类型的消息才需要重新处理
            continue;
        }
        $format_message = (new GroupAssistantService())->formatmessage($message, $tenant_access_token, $chat_id);
        // 过滤掉的消息不需要处理
        if (!$format_message) {
            $logger->info('过滤消息');
            continue;
        }

        // 列表是找不到union_id的，得去转换一下
        $open_id = $message['sender']['id'];
        $union_id = FeiShuService::getUnionIdByOpenId($open_id);
        // 找不到，则去飞书接口找一下,通过消息详情去曲线获取
        if (!$union_id) {
            $message_detail = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token, 'union_id');
            $union_id = $message_detail['data']['items'][0]['sender']['id'];
        }
        $insert_data = [
            'message_id'          => $message['message_id'],
            'chat_id'             => $chat_id,
            'decrypt'             => json_encode($message),
            'format_message'      => json_encode($format_message),
            'msg_type'            => $message['msg_type'],
            'message_create_time' => intval($message['create_time'] / 1000),
            'parent_id'           => $message['parent_id'] ?? '',
            'union_id'            => $union_id,
            'cut_off'             => $format_message['cut_off'],
            'at'                  => $format_message['at'],
        ];
        $message_model->replaceone($insert_data);

        $logger->info('消息入库完成', ['message_id' => $message['message_id']]);
    }
    $logger->info('单群处理完成', ['chat_id' => $chat_id]);
}
$logger->info('任务完成');

exit;

// 获取投放总览的指标
$target_list = (new RoutePermissionModel())->getAllByRouteIdCate(5, 3);

// 获取自定义指标
//$target_list = (new \App\Model\SqlModel\Zeda\CustomizedTargetModel())->getTargetByRouteId(5);
$embedding_model = new EmbeddingsModel();
$collection_model = new CollectionsModel();


//$vectors = $embedding_model->textEmbedding(['收入']);
//$embedding = $vectors["output"]["embeddings"][0]["embedding"] ?? [];
//$res = $collection_model->query('data_bot', $embedding, 5, 'overView');
//var_dump($res);
//exit;


// 向量化
foreach ($target_list as $item) {
    $vectors = $embedding_model->textEmbedding([$item->name]);
    $embedding = $vectors["output"]["embeddings"][0]["embedding"] ?? [];
    $ext = json_decode($item->ext, true);
    $docs = [
        [
            'id'     => $item->id,
            'vector' => $embedding,
            'fields' => ['name' => $item->name, 'column' => $ext['column'], 'user_id' => 0],
        ]
    ];
    // 插入
    $res = $collection_model->insertUpdate('data_bot', $docs, 'overView');

    echo $item->name . "处理完成\n";
    if ($res['code'] != 0) {
        echo $item->name . "处理失败\n";
        var_dump($res);
        echo PHP_EOL;
    }

}


echo '所有任务完成';
echo PHP_EOL;

