import os
from pandasai import Agent, SmartDataframe
import pandas as pd
# import pandasai.safe_libs.restricted_matplotlib as rplt_mod
from pandasai.llm import AzureOpenAI
import sys
import json
from custom_llm import CustomOpenAI
from custom_llm import CustomAzureOpenAI

# # 添加 'gca' 到白名单，具体属性名称依据 pandasai 版本可能有所不同
# if hasattr(rplt_mod, "ALLOWED_FUNCTIONS"):
#     rplt_mod.ALLOWED_FUNCTIONS.append("gca")
# elif hasattr(rplt_mod, "allowed_functions"):
#     rplt_mod.allowed_functions.append("gca")
# else:
#     # 如果没有找到白名单变量，可以尝试直接设置属性
#     setattr(rplt_mod, "gca", __import__("matplotlib.pyplot", fromlist=["gca"]).gca)
import matplotlib.pyplot as plt

user_defined_path = os.path.join(os.path.dirname(__file__), "../../srv/data_bot/pandasai_img")

# 解决中文乱码
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams["font.family"] = "sans-serif"
# 解决负号无法显示的问题
plt.rcParams['axes.unicode_minus'] = False


def json_return(code, message, data):
    return_data = {'code': code, 'message': message, 'data': data}
    print(json.dumps(return_data, ensure_ascii=False))


def read_json_file(filename):
    if not os.path.isfile(filename):
        raise FileNotFoundError(f"File '{filename}' does not exist.")

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
            return data
    except json.JSONDecodeError:
        raise ValueError(f"File '{filename}' is not a valid JSON file.")


def process_response(response):
    """处理 PandasAI 的返回结果"""
    if isinstance(response, dict):
        # 检查是否是图表结果
        if response.get('type') == 'plot':
            return {
                'type': 'string',
                'value': response['value'],
                'message': f"生成的图表已保存到: {response['value']}"
            }
        # 检查是否是数值结果
        elif response.get('type') == 'number':
            return {
                'type': 'string',
                'value': response['value'],
                'message': f"数值结果: {response['value']}"
            }
        # 检查是否是数据框结果
        elif response.get('type') == 'dataframe':
            return {
                'type': 'dataframe',
                'value': response['value'],
                'message': "返回数据框结果"
            }
    # 检查是否直接返回 DataFrame
    elif isinstance(response, pd.DataFrame):
        return {
            'type': 'dataframe',
            'value': response.to_json(orient='records', force_ascii=False),
            'message': "返回数据框结果"
        }
    # 处理字符串结果
    elif isinstance(response, str):
        return {
            'type': 'string',
            'value': response,
            'message': response
        }

    return {
        'type': 'other',
        'value': response,
        'message': f"未知类型结果: {str(response)}"
    }

def check_file_type(filename):
    if filename.endswith('.json'):
        return 'json'
    elif filename.endswith('.csv'):
        return 'csv'
    else:
        return 'unknown'


def auto_convert_dtypes(df):
    """自动推断并转换数据类型，不依赖具体列名"""
    for col in df.columns:
        # 跳过空列
        if df[col].isna().all():
            continue

        # 只处理 object 类型的列
        if df[col].dtype == 'object':
            # 尝试转换为日期类型（检测常见的日期格式）
            sample_values = df[col].dropna().head(5).astype(str)
            date_indicators = ['-', '/', '年', '月', '日', ':', 'T']

            # 检查是否包含日期特征
            has_date_pattern = any(
                any(indicator in str(val) for indicator in date_indicators)
                for val in sample_values
            )

            if has_date_pattern:
                try:
                    converted = pd.to_datetime(df[col], errors='coerce')
                    # 如果转换成功率超过80%，认为是日期列
                    if converted.notna().sum() / len(df) > 0.8:
                        df[col] = converted
                        continue
                except:
                    pass

            # 尝试转换为数值类型
            try:
                converted = pd.to_numeric(df[col], errors='coerce')
                # 如果转换成功率超过80%，认为是数值列
                if converted.notna().sum() / len(df) > 0.8:
                    df[col] = converted
            except:
                pass

    return df


if __name__ == '__main__':
    if len(sys.argv) != 3:
        json_return(-1, '错误的传参', {})
        sys.exit(1)

    data_filename = sys.argv[1]
    query_filename = sys.argv[2]

    try:

        # 加载 json 数据
        file_type = check_file_type(data_filename)
        if file_type == 'csv':
            # 先读取 CSV 文件为 DataFrame
            df = pd.read_csv(data_filename)
        elif file_type == 'json':
            json_data = read_json_file(data_filename)
            # # 将 JSON 数据转换为 DataFrame
            df = pd.DataFrame(data=json_data["data_list"], columns=json_data["column_list"])
        else:
            json_return(-1, '错误的文件类型', {})
            sys.exit(1)

        # 用户查询的数据
        query_data = read_json_file(query_filename)
        query = query_data["content"]
        history_message_list = query_data["history_message_list"]

        # 创建 LLM
        # 模型
        llm = CustomOpenAI(
            api_token="87172cf8-2858-4692-a039-14405818caa1",
            api_base="https://ark.cn-beijing.volces.com/api/v3",
            model="ep-20250806143550-wlqfh",
            history_message_list=history_message_list,
        )
        # 微软的 OpenAI
        # llm = CustomAzureOpenAI(
        #     api_token="69e6ad9453fb4bd2b0f387efd37d940e",
        #     azure_endpoint="http://tw-openai-wus3-azure.zxzt123.com",
        #     api_version="2024-09-01-preview",
        #     deployment_name="gpt-4o"
        # )

        # 如果 DataFrame 的行数大于 1，则去掉第一行合计行
        if len(df) > 1:
            df = df.iloc[1:]

        # 应用自动类型转换
        df = auto_convert_dtypes(df)

        # 确保数据按照原始顺序，重置索引
        df = df.reset_index(drop=True)
        sdf = SmartDataframe(df, config={
            "llm": llm,
            "plotting_backend": "matplotlib",  # 使用原生版本
            "save_charts": True,
            "save_charts_path": user_defined_path,
            "open_charts": False,
            "enable_cache": False,
            "security": "none"
        })

        response = sdf.chat(query)

        # print("=== 返回结果 ===")
        # print(response)
        # print(type(response))
        response = process_response(response)

        json_return(0, 'success', response)
    except Exception as e:
        json_return(-1, str(e), {})
