<?php

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Volcengine\Knowledge\PointModel;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Service\DataBot\DataBotPy;
use App\Service\DataBot\DataBotService;
use App\Service\DataBot\DataBotSession;
use App\Service\EnterpriseDiDiService;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageFormat;
use App\Service\GroupAssistant\MessageSummary;
use App\Struct\RedisCache;
use App\Task\ExportFileTask;
use App\Utils\Helpers;
use Exception;
use Illuminate\Support\Collection;

class TestController extends Controller
{
    protected $pass_method = [
        'exportFileTask',
        'dataBot',
        'knowledge',
        'handlerText',
        'test',
        'didi',
        'addMessage'
    ];

    public function didi()
    {
//        $res = (new ApprovalModel())->getOrder('广州中旭未来科技有限公司', '1125973836630895');

        $res = (new EnterpriseDiDiService())->isApplicationUsed('广州中旭未来科技有限公司', '1125973836630895');

        dd($res);
        $service = new EnterpriseDiDiService();

        $data = '{"uuid":"73c5c8d4ead1d6d84f8b06e4b7656565","event":{"app_id":"cli_a75258559bf8900b","approval_code":"E83AE510-3A47-4126-B7B0-9C0ACE8DB07D","end_time":1750846659,"i18n_resources":[{"is_default":true,"locale":"zh_cn","texts":{"@i18n@48C02BDA8D205CACE9521A4FE292BE34":"公事外出"}}],"instance_code":"DA03CD15-AC59-4B47-98A2-7C94A9027C29","open_id":"ou_3e2622758081e884c302a06a03d39c6f","out_end_time":"2025-06-26 18:30:00","out_image":"","out_interval":21600,"out_name":"@i18n@48C02BDA8D205CACE9521A4FE292BE34","out_reason":"外出参加网络游戏出版分享会","out_start_time":"2025-06-26 12:30:00","out_unit":"HOUR","start_time":1750843309,"tenant_key":"105189295a17175f","type":"out_approval","user_id":"TW7606"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750846660.054733","type":"event_callback"}';

        $decrypt = json_decode($data, true);
        $service->outApproval($decrypt);
        dd(1);
        //        // 清除缓存
//        $date = '2025-06-23';
//        $employee_no_list = [
//            'TW6513',
//            'TW4573',
//            'TW7102',
//        ];
//        foreach ($employee_no_list as $employee_no) {
//            $service->cleanCache($employee_no, $date);
//        }
////
//        dd(1);

        // 模拟用户打卡
        $data = '{"schema":"2.0","header":{"event_id":"a83c0ddb4cce8c9757fba0318df00d2c","token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","create_time":"1750688537141","event_type":"attendance.user_flow.created_v1","tenant_key":"105189295a17175f","app_id":"cli_a75258559bf8900b"},"event":{"bssid":"14:84:77:84:31:62","check_time":"1750688533","comment":"","employee_id":"TW7102","employee_no":"TW7102","is_field":false,"is_wifi":true,"latitude":0,"location_name":"Tanwan(14:84:77:84:31:62)","longitude":0,"photo_urls":[],"record_id":"7519149999227912194","risk_result":0,"ssid":"Tanwan","type":0}}';


        $decrypt = json_decode($data, true);
        $service->userClockOut($decrypt);
        dd(1);


        // 模拟用户审批通过
        $data = '{"uuid":"00eed76c4d5ec58b5251bf6a1c8353f0","event":{"app_id":"cli_a75258559bf8900b","approval_code":"744AD97C-BF36-414C-A8FF-06804111B5FF","instance_code":"E6140BB1-1062-4ACD-A31F-F31F8B9AD2F1","instance_operate_time":"1750688511343","operate_time":"1750688511343","status":"APPROVED","tenant_key":"105189295a17175f","type":"approval_instance","uuid":"c25bfb5b"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750688512.992143","type":"event_callback"}';
        $decrypt = json_decode($data, true);

        $service->clockOutApproval($decrypt);
        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    /**
     * 测试
     *
     * @return array
     */
    public function test()
    {

        $json_message = 'v1: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';

//        $json_message = 'v1:dI6sF/ahxB9S5ijWiOgCA2mxLSxtjYe1lPPW+3+xa8L4+Tn1XDfxmfesHwdO1yUKJwGeI+gNZ5E5ufS7QnRuEfUoRUleylUYus8ADVUwti7Vj0h+o1otcxapEHbHAldJSkEZwcp4VicYLdyPcUm9OMW9fzyFAhGcEp+TyThPGYMvdu4Mc9ga3TJXWTw6G6SzTumwmk2SefIo94KR9N9eVz76bJnHBcI97QqlbuAJmKPvW5yEgCYzbRHyuXI=';
        $a = MessageFormat::decryptJsonMessage($json_message);
        dd(json_decode($a, true));

        $service = new  EnterpriseDiDiService();

        $dd_user_info = $service->getUserInfoByDidi('TW0985', 2);
        $logic = new WechatDataLogic();
        $start_time = 1752768000 - 86400;
        $user_id = 151;
        $theme = '测试推送';
        $list = $logic->getDataSumPush($start_time, $user_id, $theme);
        return [
            'list'         => $list,
            'dd_user_info' => $dd_user_info,
        ];
    }

    public function exportFileTask()
    {
        $row_data = (new ExportFileTaskModel())->getUnFinishOne();
        $data = (new ExportFileTask())->getSettlementSummarizeList($row_data);

        return [
            'data' => $data,
        ];
    }

    /**
     * 知识问答测试
     *
     * @return array
     * @throws Exception
     */
    public function knowledge()
    {
        //        // 获取消息内容，重新总结
//        $message_list = (new FeiShuMessageModel())->getListByChatIdAndLastTime('oc_7cbabbc3c5ba6f6d2e1c4d2bd35476b9','2025-07-09 00:00:00');
//
//        $message_list = GroupAssistantService::formatToLLMMessageList($message_list);
//        $message_summary = new MessageSummary();
//
//        $summary = $message_summary->generateSummary($message_list, '');
//
//        dd($summary);


        $query = '之前有评测过《Trouble Squad》这个游戏，套用一下这个模版，可能需要注意哪些评测细节？如果有参考链接，请给出参考链接。';
        $union_id = 'on_ee55ca12cb03c11fc39c54e4aa705965'; // 仇
        $chat_id = 'oc_973d16003f0ebf92318ee96b4370e996';

//        $union_id = 'on_b8ef743bfe285ce29e8079b882739aa3'; // dai
        $chat_id = '';


        // 清空session
//        $redis = RedisCache::getInstance();
//        $session_key = KnowledgeService::QA_SESSION_KEY . "group_{$chat_id}";
//        $redis->del($session_key);
//        $session_key = GroupAssistantService::INTENT_SESSION_KEY . $union_id;
//
//        $redis->del($session_key);
//        $session_key = KnowledgeService::QA_SESSION_KEY . $union_id;
//        $redis->del($session_key);


        //        // 意图识别
//        $result = (new GroupAssistantService())->intent($query, $chat_id, 'p2p', $union_id);
        ////
//        dd($result);
        $param = [
            'query'      => $query,
            'union_id'   => $union_id,
            'chat_id'    => $chat_id,
            'type'       => 'p2p',
            'start_date' => '',
            'end_date'   => ''
        ];
        // 知识问答
        $param = new KnowledgeParam($param);

        // 初始化流式输出的卡片参数 这里固定用dhq的union_id接受消息
        $union_id = 'on_b8ef743bfe285ce29e8079b882739aa3';
        $message_param = new FeishuStreamCardParam([
            'receive_id'          => $union_id,
            'receive_id_type'     => 'union_id',
            'tenant_access_token' => (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET),
            'content'             => '正在解析消息...',
        ]);

        // 发个消息让用户等待一下
        FeiShuService::stream($message_param);

        (new KnowledgeService())->getKnowledgeAnswer($param, $message_param);

        FeiShuService::updateStreamModel($message_param);

        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    public function dataBot()
    {
        $data_bot_session = new DataBotSession(151, 1);
        $data_bot_service = new DataBotService('戴焕其', 151, Helpers::getLogger('wechat_data_bot'));
        $data_bot_session->cleanSession();

        $user_id = 151;
        //        $user_query = '再加一个注册数指标';
//        $user_query = '前十的呢？';
        $user_query = '查询野兽领主最近一个月的总付费金额。并且按每日的流水趋势画一个折线图';


        $output = $data_bot_service->run($user_query, 1);

        // 是否是数据分析
        $analyse = $output['analyse'] ?? false;

        if ($analyse) {
            // 多维度数据先不考虑，直接拿一条先 TODO
            $table_list = $output['draw_data'][0] ?? [];
            $analyse_respond = DataBotPy::analyse($user_query, 1, $table_list, $user_id, Helpers::getLogger('wechat_data_bot'));

            $output['analyse_respond'] = $analyse_respond;

        } else {
            $draw_data = $output['draw_data'];
            // 循环，返回多张图
            foreach ($draw_data as $data_item) {
                // 生成图片
                $png_filename = DataBotPy::plotting(1, $data_item, $user_id, Helpers::getLogger('wechat_data_bot'));
            }
        }


        $route_name = RedisCache::getInstance()->get($data_bot_session->genRouterNameSessionKey(1, 151));
        $output['session_list'] = $data_bot_session->getSessionList($data_bot_session->genDataBotSessionKey($route_name, 1, 151));
        $output['dispatch_router'] = $data_bot_session->getSessionList($data_bot_session->genDispatchRouteSessionKey(1, 151));
        $output['all_session_list'] = DataBotSession::getAllConversationHistory(1, $user_id);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $output
        ];
    }


    public function handlerText()
    {
        $message_id = 'om_x100b4a9397f764240f3fbb96378cd51';

        $file_key = 'file_v3_00ni_29f35640-5433-4ed5-87f9-9e4a1f6a64bg';
        $filename = '202505-江西贪玩-任德权-神器-云账户-cps（抖音、快手）1.xlsx';
        $cut_off = 0;
        MessageFormat::handlerFile($message_id, $file_key, $filename, $cut_off);

        dd(1);
        // 示例用法
        $cut_off = 0;
        $inputText = "https://lx3qcyzne8.feishu.cn/wiki/S9sJwhUiIiTb65kmPNJctldtnde?sheet=50pXYr在线表的都看看啊";
        $outputText = MessageFormat::handlerText($inputText, 'om_46d1c07ea1d2c5c39ee5d149e196b6b0', $cut_off, false);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $outputText
        ];
    }

    public function addMessage()
    {
        $logger = Helpers::getLogger('add_message');

        // 需要添加的群id
        $chat_id = 'oc_973d16003f0ebf92318ee96b4370e996';
        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        // 先去获取聊天记录
        $start_time = strtotime(date("Y-m-d H:i:s", strtotime("2025-08-01")));
        $end_time = strtotime(date("Y-m-d H:i:s", strtotime("2025-08-13")));
        $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time, 50, 'ByCreateTimeAsc');


        $logger->info('获取到了消息历史，开始解析聊天记录', ['chat_id' => $chat_id, 'message_list' => $message_list]);
        $message_model = new FeiShuMessageModel();

        $service = new GroupAssistantService();
        // 解析聊天记录
        foreach ($message_list as $message) {

//            // 这里还可以指定消息id
//            if ($message['message_id'] !== 'om_x100b466d2a55ec2c0f49f5de06908a5') {
//                continue;
//            }


            // 先获取access_token 防止长时间循环token过期
            $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

            $format_message = $service->formatmessage($message, $tenant_access_token, $chat_id);
            // 过滤掉的消息不需要处理
            if (!$format_message) {
                $logger->info('过滤消息', ['chat_id' => $chat_id]);
                continue;
            }

            // 列表是找不到union_id的，得去转换一下
            $open_id = $message['sender']['id'];
            $union_id = FeiShuService::getUnionIdByOpenId($open_id);
            // 找不到，则去飞书接口找一下,通过消息详情去曲线获取
            if (!$union_id) {
                $message_detail = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token, 'union_id');
                $union_id = $message_detail['data']['items'][0]['sender']['id'];
            }
            $insert_data = [
                'message_id'          => $message['message_id'],
                'chat_id'             => $chat_id,
                'decrypt'             => json_encode($message),
                'format_message'      => json_encode($format_message),
                'msg_type'            => $message['msg_type'],
                'message_create_time' => intval($message['create_time'] / 1000),
                'parent_id'           => $message['parent_id'] ?? '',
                'union_id'            => $union_id,
                'cut_off'             => $format_message['cut_off'],
                'at'                  => $format_message['at'],
            ];
            $message_model->replaceone($insert_data);

            $logger->info('消息入库完成', ['chat_id' => $chat_id, 'message_id' => $message['message_id']]);
        }

        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    /**
     * 更新切片
     * @return array
     */
    public function addSummary()
    {
        $logger = Helpers::getLogger('add_summary');

        dd(1);
        (new MessageSummary())->addSummary();

        return [
            'code' => ResponseCode::SUCCESS,
        ];

    }

}