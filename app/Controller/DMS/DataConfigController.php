<?php

namespace App\Controller\DMS;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Logic\DMS\DataConfigLogic;
use App\Logic\DMS\PermissionLogic;
use App\Service\PermissionService;
use App\Struct\Input;

class DataConfigController extends Controller
{
    /**
     * @CtrlAnnotation(permissions=['/twdblog/data-config'])
     * @param Input $input
     *
     * @return array
     */
    public function permission(Input $input)
    {
        $input->verify(['level', 'rank_id']);

        $route_name = $this->request->header['router'];
        $route_id = RouteID::ROUTE_NAME_MAP[$route_name];

        $service = new PermissionService();
        $data = $service->getRankRoutePermission((int)$input['level'], (int)$input['rank_id'], $route_id);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '',
            'data' => $data
        ];
    }

    /**
     * 新增渠道配置
     * @CtrlAnnotation(permissions=['/twdblog/data-config'], log_type='add')
     *
     * @param Input $input
     *
     * @return array
     */
    public function addAgentConfig(Input $input)
    {
        $input->verify(['platform', 'type']);

        $platform = $input['platform'];
        $type = intval($input['type']);
        $agent_group_id = $input['agent_group_id'] ?? [];
        $agent_id = $input['agent_id'] ?? [];

        // 区分渠道组录入和渠道录入
        if ($type == 1) {
            $input->verify(['agent_group_id']);

        } elseif ($type == 2) {
            $input->verify(['agent_id']);

        } else {
            throw new AppException('录入类型错误');
        }

        (new DataConfigLogic())->addAgentConfig($platform, $type, $agent_group_id, $agent_id, Container::getSession()->name);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * 获取渠道配置列表
     * @CtrlAnnotation(permissions=['/twdblog/data-config'], log_type='get')
     *
     * @param Input $input
     *
     * @return array
     */
    public function getAgentConfigList(Input $input)
    {
        $keyword = $input['keyword'] ?? '';
        $agent_permission = (new PermissionLogic())->getLoginUserAgentPermission();
        $list = (new DataConfigLogic())->getAgentConfigList($agent_permission, $keyword);

        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $list,
            'message' => '添加成功',
        ];
    }


    /**
     * 删除渠道配置
     * @CtrlAnnotation(permissions=['/twdblog/data-config'], log_type='delete')
     *
     * @param Input $input
     *
     * @return array
     */
    public function deleteAgentConfig(Input $input)
    {
        $input->verify(['platform', 'agent_id']);

        (new DataConfigLogic())->deleteAgentConfig($input['platform'], $input['agent_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }
}
