<?php

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Logic\DSP\ToutiaoStarLogic;
use App\Param\ADServing\ToutiaoStar\ComposeParam;
use App\Param\ToutiaoStarComponentTaskParam;
use App\Response\Response;
use App\Service\NoticeService;
use App\Struct\Input;
use App\Utils\UploadTool;
use Exception;

class ToutiaoStarController extends Controller
{

    protected $pass_method = ['message'];

    /**
     * 实时控制广告任务状态
     * @param Input $input
     * @return array
     */
    public function message(Input $input)
    {
        $notice_service = new NoticeService();
        $notice_service->unicast($input['creator_id'], NoticeService::NOTICE_TOUTIAO_STAR_TASK_UPDATE, $input->getData());

        foreach ([326, 261, 27] as $user_id) {
            $notice_service->unicast($user_id, NoticeService::NOTICE_TOUTIAO_STAR_TASK_UPDATE, $input->getData());
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发送成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-aweme'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAwemeList(Input $input)
    {
        $data = (new ToutiaoStarLogic())->getAwemeList(
            $input['platform'] ?? '',
            $input['aweme_id'] ?? '',
            $input['aweme_name'] ?? '',
            $input['page'] ?? 1,
            $input['rows'] ?? 20,
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-aweme'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function importAweme(Input $input)
    {
        $input->verify(['platform']);
        $data_list = UploadTool::csv($this->request->files);
        (new ToutiaoStarLogic())->importAweme($input['platform'], $data_list);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '导入成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-aweme'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAweme(Input $input)
    {
        $input->verify(['platform', 'aweme_id', 'aweme_name']);
        (new ToutiaoStarLogic())->addAweme($input['platform'], $input['aweme_id'], $input['aweme_name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-aweme'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editAweme(Input $input)
    {
        $input->verify(['id', 'aweme_id', 'aweme_name']);
        (new ToutiaoStarLogic())->editAweme($input['id'], $input['aweme_id'], $input['aweme_name']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '修改成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-aweme'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteAweme(Input $input)
    {
        $input->verify(['id']);
        (new ToutiaoStarLogic())->deleteAweme($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getAwemeOptions(Input $input)
    {
        $result = (new ToutiaoStarLogic())->getAwemeOptions($input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $result,
            'message' => '获取数据成功',
        ];
    }

    /**
     * @param Input $input
     * @return \App\Response\CSV
     */
    public function downloadAwemeTemplate(Input $input)
    {
        $head = ['达人ID', '达人名称'];
        $data_list = [['10086', '达人名称']];

        $download_filename = '达人信息导入模板.csv';
        // 源文件名称
        $source_filename = md5(microtime(true)) . '.csv';
        $csv = Response::CSV($download_filename, $source_filename);
        $content = implode(',', $head) . PHP_EOL;
        foreach ($data_list as $item) {
            $content .= implode(',', $item) . PHP_EOL;
        }
        $str = mb_convert_encoding($content, 'GB18030', 'utf-8');
        file_put_contents($csv->path . '/' . $source_filename, $str, FILE_APPEND);
        return $csv;
    }

    /**
     * 获取组合列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getComposeList(Input $input)
    {
        $data = (new ToutiaoStarLogic())->getComposeList(
            $input->getData(),
            $input['page'] ?? 1,
            $input['rows'] ?? 20,
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取组合
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getComposeInfo(Input $input)
    {
        $input->verify(['id']);
        $data = (new ToutiaoStarLogic())->getComposeInfo(
            $input['id'],
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 删除组合
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function removeCompose(Input $input)
    {
        $input->verify(['id']);
        (new ToutiaoStarLogic())->removeCompose($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 保存组合
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function operateCompose(Input $input)
    {
        $input->verify(['platform']);
        (new ToutiaoStarLogic())->operateCompose(new ComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='add')
     * 保存组合并发布任务
     * @param Input $input
     * @return array
     */
    public function operateComposeAndPublish(Input $input)
    {
        $input->verify(['name', 'platform']);
        $result = (new ToutiaoStarLogic())->operateComposeAndPublish(new ComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result,
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='add')
     * 校验组合
     * @param Input $input
     * @return array
     */
    public function validateCompose(Input $input)
    {
        $input->verify(['platform']);
        $result = (new ToutiaoStarLogic())->validateCompose(new ComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $result,
        ];
    }

    /**
     * 发布任务
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function publishTask(Input $input)
    {
        $result = (new ToutiaoStarLogic())->publishTask(new ComposeParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发布成功',
            'data' => $result,
        ];
    }

    /**
     * 重启任务
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-task-list'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function restartTask(Input $input)
    {
        $input->verify(['id']);
        (new ToutiaoStarLogic())->restartTask($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 重建任务
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-task-list'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function rebuildTask(Input $input)
    {
        $input->verify(['id']);
        (new ToutiaoStarLogic())->rebuildTask($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 获取任务列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-task-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTaskList(Input $input)
    {
        $data = (new ToutiaoStarLogic())->getTaskList(
            $input->getData(),
            $input['page'] ?? 1,
            $input['rows'] ?? 20,
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * 获取公司列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'])
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function getCompanyList(Input $input)
    {
        $input->verify(['platform']);
        $data = (new ToutiaoStarLogic())->getCompanyList(
            $input['platform'],
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data
            ]
        ];
    }

    /**
     * 星图资源列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='get')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function getVideoList(Input $input)
    {
        $data = (new ToutiaoStarLogic())->getVideoList(
            $input->getData(),
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function refreshOrder(Input $input)
    {
        $input->verify(['order_id']);
        (new ToutiaoStarLogic())->refreshOrder(
            $input['order_id']
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '刷新成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function refreshAloneOrder(Input $input)
    {
        $input->verify(['order_id','resource_id']);
        $data = (new ToutiaoStarLogic())->refreshAloneOrder(
            $input['order_id'],
            $input['resource_id'],
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '刷新成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='get')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function getPending(Input $input)
    {
        $input->verify(['order_id']);
        $data = (new ToutiaoStarLogic())->getPending(
            $input['order_id'],
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function payOrder(Input $input)
    {
        $input->verify(['order_id']);
        $data = (new ToutiaoStarLogic())->payOrder(
            $input['order_id'],
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function cancelOrder(Input $input)
    {
        $input->verify(['order_id', 'reason_type']);
        $data = (new ToutiaoStarLogic())->cancelOrder(
            $input['order_id'], $input['reason_type'], $input['reason'] ?? ''
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function publishOrder(Input $input)
    {
        $input->verify(['order_id']);
        $data = (new ToutiaoStarLogic())->publishOrder(
            $input['order_id'], $input['schedule_time'] ?? 0
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function confirmResource(Input $input)
    {
        $input->verify(['order_id', 'ad_sync_decision']);
        $data = (new ToutiaoStarLogic())->confirmResource(
            $input['order_id'], $input['ad_sync_decision']
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-video-list'], log_type='edit')
     * @param $media_type
     * @param $platform
     * @return array
     */
    public function pushResource(Input $input)
    {
        $input->verify(['order_id', 'push_account_ids']);
        $data = (new ToutiaoStarLogic())->pushResource(
            $input['order_id'], $input['push_account_ids']
        );
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '推送成功',
            'data' => $data
        ];
    }

    /**
     * 获取项目列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getProjectList(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getProjectList($input['account_id'], $input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取品牌列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getBrandList(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getBrandList($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取联系人列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getContactData(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getContactData($input['account_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取小手柄组件
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getHandleComponentList(Input $input)
    {
        $input->verify(['game_name','account_id']);
        $data = (new ToutiaoStarLogic())->getHandleComponentList($input['account_id'], $input['game_name'], $input['component_name'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取小手柄组件
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getStarComponent(Input $input)
    {
        $input->verify(['handle_component_id','account_id']);
        $data = (new ToutiaoStarLogic())->getStarComponent($input['account_id'], $input['handle_component_id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取组件筛选游戏名称
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getComponentGameNameList(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getComponentGameNameList($input['account_id'], $input['keyword'] ?? '');
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取直播达人列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLiveAuthorList(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getLiveAuthorList($input['account_id'], $input['list_id'] ?? 0, $input['keyword'] ?? '',$input['page'] ?? 1,$input['rows'] ?? 20);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取达人清单列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-compose-list'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAuthorListIds(Input $input)
    {
        $input->verify(['account_id']);
        $data = (new ToutiaoStarLogic())->getAuthorListIds($input['account_id'], $input['page'] ?? 1,$input['rows'] ?? 100);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 获取组件任务列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-component'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getComponentTaskList(Input $input): array
    {
        $data = (new ToutiaoStarLogic())->getComponentTaskList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 添加组件任务
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-component'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function addComponentTask(Input $input): array
    {
        $input->verify(['platform','account_id_list','component_name','component_data']);
        $data = (new ToutiaoStarLogic())->addComponentTask(new ToutiaoStarComponentTaskParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '成功',
            'data' => $data
        ];
    }

    /**
     * 重启组件任务
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-component'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function restartComponentTask(Input $input): array
    {
        $input->verify(['id']);
        (new ToutiaoStarLogic())->restartComponentTask($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 上传主播身份证
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function uploadLiveAnchorIDCard(Input $input): array
    {
        $data = (new ToutiaoStarLogic())->uploadliveAnchorIDCard($this->request->files);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '操作成功',
        ];
    }

    /**
     * 上传机构营业执照
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function uploadLiveBusiness(Input $input): array
    {
        $data = (new ToutiaoStarLogic())->uploadLiveBusiness($this->request->files);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
            'message' => '操作成功',
        ];
    }

    /**
     * 上传主播信息
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function enterLiveAnchor(Input $input): array
    {
        $input->verify([
            'anchor_event', 'live_hour', 'price', 'price_type', 'start_date','is_extra'
        ]);
        if ($input['anchor_event'] == 2) {
            $input->verify(['true_name', 'anchor_name', 'telephone', 'company']);
        }else{
            $input->verify(['anchor_id']);
        }
        $data = (new ToutiaoStarLogic())->enterLiveAnchor($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }

    /**
     * 主播信息查询
     * @param Input $input
     * @return array
     */
    public function liveAnchorOptions(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->liveAnchorOptions($input['name'] ?: ''),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播信息查询
     * @param Input $input
     * @return array
     */
    public function getCurrentAnchorPrice(Input $input): array
    {
        $input->verify(['anchor_id' => ['required'], 'publish_time'=>['required']]);
        return [
            'data' => (new ToutiaoStarLogic())->getCurrentAnchorPrice($input['anchor_id'], $input['publish_time']),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播信息查询
     * @param Input $input
     * @return array
     */
    public function getCurrentAnchorPriceList(Input $input): array
    {
        $input->verify(['anchor_ids' => ['required']]);
        return [
            'data' => (new ToutiaoStarLogic())->getCurrentAnchorPriceList($input['anchor_ids'], $input['publish_time'] ?? ''),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLiveAnchorList(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->getLiveAnchorList($input->getData(), $input['page'] ?? 1, $input['rows'] ?? 50),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播单价录入列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAnchorPriceList(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->getAnchorPriceList($input->getData(), $input['page'] ?? 1, $input['rows'] ?? 50),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播单价录入列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function editAnchor(Input $input): array
    {
        $input->verify(['id','telephone','anchor_name']);
        (new ToutiaoStarLogic())->editAnchor($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 主播单价录入列表
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function editAnchorPrice(Input $input): array
    {
        $input->verify(['id', 'price', 'live_hour']);
        $data = (new ToutiaoStarLogic())->editAnchorPrice($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }

    /**
     * 根据身份证获取主播信息
     * @param Input $input
     * @return array
     */
    public function getAnchorInfoByIdCard(Input $input): array
    {
        $input->verify(['idcard' => ['required']]);
        return [
            'data' => (new ToutiaoStarLogic())->getAnchorInfoByIdCard($input['idcard']),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @param Input $input
     * @return array
     */
    public function liveCompanyOptions(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->liveCompanyOptions($input['name'] ?: ''),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addLiveCompany(Input $input): array
    {
        $input->verify([
            'company'
        ]);
        (new ToutiaoStarLogic())->addLiveCompany($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function editLiveCompany(Input $input): array
    {
        $input->verify([
            'company'
        ]);
        (new ToutiaoStarLogic())->editLiveCompany($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getLiveCompanyList(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->getLiveCompanyList($input->getData(), $input['page'] ?: 1, $input['rows'] ?: 50 ),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getAnchorOperateLog(Input $input): array
    {
        return [
            'data' => (new ToutiaoStarLogic())->getAnchorOperateLog($input->getData(), $input['page'] ?: 1, $input['rows'] ?: 50 ),
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
        ];
    }

    /**
     * 获取主播审核中价格
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getInApproveAnchorPriceInfo(Input $input): array
    {
        $input->verify([
            'anchor_log_id', 'price_type'
        ]);

        $data = (new ToutiaoStarLogic())->getInApproveAnchorPriceInfo($input['anchor_log_id'], $input['price_type']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '操作成功',
            'data' => $data
        ];
    }

    /**
     * 获取直播的渠道负责人
     * @CtrlAnnotation(permissions=['/ad-asset/toutiao-star-live-anchor'], log_type='get')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getLiveAgentLeader(Input $input): array
    {
        $data = (new ToutiaoStarLogic())->getLiveAgentLeader($input['keyword'] ?? '');

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data
        ];
    }
}
