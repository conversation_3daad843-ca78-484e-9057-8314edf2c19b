<?php
/**
 * 广告批量投放模块-适配多媒体
 * User: 张中昊
 * Date: 2020/02/21
 * Time: 15:45
 */

namespace App\Controller\DSP;

use App\Constant\ResponseCode;
use App\Constant\RouteID;
use App\Constant\InitIntelligentMonitor;
use App\Exception\AppException;
use App\Logic\DSP\ADServingLogic;
use App\Logic\DSP\MaterialLogic;
use App\Logic\DSP\PermissionLogic;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Param\ADServing\ADIntelligentComposeConditionSearchParam;
use App\Param\ADServing\ADIntelligentComposeLogSearchParam;
use App\Param\ADServing\ADIntelligentComposeParam;
use App\Param\ADServing\ADIntelligentComposeSearchParam;
use App\Param\ADServing\ADIntelligentMonitorBindLogSearchParam;
use App\Param\ADServing\ADIntelligentMonitorExecBodyParam;
use App\Param\ADServing\ADIntelligentMonitorExecBodySearchParam;
use App\Param\ADServing\ADIntelligentMonitorRobotLogSearchParam;
use App\Param\ADServing\ADIntelligentMonitorRobotParam;
use App\Param\ADServing\ADIntelligentMonitorRobotSearchParam;
use App\Param\ADServing\ADTaskSearchParam;
use App\Param\ADServing\ADTaskLogSearchParam;
use App\Service\MediaAD\MediaAD;
use App\Service\NoticeService;
use App\Struct\Input;
use Exception;
use Throwable;

/**
 * 广告批量投放
 * Class ADServingController
 * @package App\Controller
 */
class ADServingController extends Controller
{
    protected $pass_method = ['addADTaskByApi', 'message'];

    /**
     * 实时控制广告任务状态
     * @param Input $input
     * @return array
     */
    public function message(Input $input)
    {
        $notice_service = new NoticeService();
        $notice_service->unicast($input['creator_id'], NoticeService::NOTICE_AD_TASK_UPDATE, $input->getData());

        foreach ([326, 261, 27] as $user_id) {
            $notice_service->unicast($user_id, NoticeService::NOTICE_AD_TASK_UPDATE, $input->getData());
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '发送成功',
        ];
    }

    //-------------------------------------定向包-----------------------------------------------------------------------

    /**
     * 生成一个定向包信息
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTargetingDataByAD2(Input $input)
    {
        $input->verify(['media_type', 'company', 'md5']);
        $ad_service = new MediaAD($input['media_type']);
        try {
            $data = $ad_service->getTargetingDataByAD2(
                $input['company'],
                $input['md5']
            );
            return [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功',
                'data' => $data,
            ];
        } catch (Throwable $e) {
            return [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 根据一个md5判断是否已存在定向包
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTargetingExitByMD5(Input $input)
    {
        $input->verify(['media_type', 'company', 'md5']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->getTargetingExitByMD5($input->media_type, $input->company, $input->md5);
            if (!$data) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'action' => 0,
                    'data' => [],
                    'message' => '添加成功'
                ];
            } else {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'action' => 1,
                    'data' => $data,
                    'message' => '已存在同样的定向包'
                ];
            }
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 添加一个定向包
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addTargetingPacket(Input $input)
    {
        $input->verify(['media_type', 'targeting']);
        try {
            $ad_logic = new ADServingLogic();
            $add_data = $ad_logic->addTargetingPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $add_data['state'] ? '新建成功' : '新建失败',
                'data' => [
                    'result' => $add_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 修改一个定向包
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editTargetingPacket(Input $input)
    {
        $input->verify(['id', 'media_type', 'targeting']);
        try {
            $ad_logic = new ADServingLogic();
            $edit_data = $ad_logic->editTargetingPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $edit_data['state'] ? '修改成功' : '修改失败',
                'data' => [
                    'result' => $edit_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取定向包列表
     * @param Input $input
     * @return array
     */
    public function getTargetingPacketList(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getTargetingPacketList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取一个定向包内容
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTargetingPacketById(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getTargetingInfoById($input['id']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'info' => $data
            ],
        ];
    }

    /**
     * 获取广告计划行为列表
     * @param Input $input
     * @return array
     */
    public function getAdActionList(Input $input)
    {
        $input->verify(['media_type']);
        $media_ad_service = new MediaAD($input['media_type']);
        $data = $media_ad_service->getAdActionList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data,
                'total' => count($data)
            ],
        ];
    }

    /**
     * 获取计划行为徕卡id转词汇
     * @param Input $input
     * @return array
     */
    public function getActionWord(Input $input)
    {
        $input->verify(['media_type']);
        $media_ad_service = new MediaAD($input['media_type']);
        $data = $media_ad_service->getActionWord($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'info' => $data
            ],
        ];
    }

    /**
     * 获取广告计划兴趣列表
     * @param Input $input
     * @return array
     */
    public function getAdInterestList(Input $input)
    {
        $input->verify(['media_type']);
        $media_ad_service = new MediaAD($input['media_type']);
        $data = $media_ad_service->getAdInterestList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => $data,
                'total' => count($data)
            ],
        ];
    }

    /**
     * 获取计划兴趣徕卡id转词汇
     * @param Input $input
     * @return array
     */
    public function getInterestWord(Input $input)
    {
        $input->verify(['media_type']);
        $media_ad_service = new MediaAD($input['media_type']);
        $data = $media_ad_service->getInterestWord($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'info' => $data
            ],
        ];
    }

    /**
     * 删除定向包
     * @CtrlAnnotation(permissions=['/ad-create/targeting'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteTargetingPacket(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->deleteTargetingPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //---------------------------------------------------文案包----------------------------------------------------------

    /**
     * 添加一个文案包
     * @CtrlAnnotation(permissions=['/ad-create/word'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addWordPacket(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->addWordPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新文案包
     * @CtrlAnnotation(permissions=['/ad-create/word'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editWordPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->editWordPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取文案体列表
     * @param Input $input
     * @return array
     */
    public function getWordPacketList(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getWordPacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 删除一个文案包
     * @CtrlAnnotation(permissions=['/ad-create/word'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteWordPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->deleteWordPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //-------------------------------------------------标签包-----------------------------------------------------------

    /**
     * 添加一个标签包
     * @CtrlAnnotation(permissions=['/ad-create/tag'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addTagPacket(Input $input)
    {
        $input->verify(['media_type']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->addTagPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新标签包
     * @CtrlAnnotation(permissions=['/ad-create/tag'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editTagPacket(Input $input)
    {
        $input->verify(['media_type', 'id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->editTagPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取标签包
     * @param Input $input
     * @return array
     */
    public function getTagPacketList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getTagPacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 删除标签包
     * @CtrlAnnotation(permissions=['/ad-create/tag'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteTagPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->deleteTagPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //--------------------------------------------------素材包----------------------------------------------------------

    /**
     * 复制一个素材包
     * @CtrlAnnotation(permissions=['/ad-create/material'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function copyMaterialPacket(Input $input): array
    {
        $input->verify(['media_type', 'ids']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->copyMaterialPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 创建一个素材包
     * @CtrlAnnotation(permissions=['/ad-create/material'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addMaterialPacket(Input $input)
    {
        $input->verify(['media_type']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->addMaterialPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 创建一个素材包
     * @CtrlAnnotation(permissions=['/ad-create/material'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addMaterialPacketForAdA(Input $input)
    {
        $input->verify(['media_type', 'ids', 'name']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->addMaterialPacketForAdA($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '添加成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新一个素材包
     * @CtrlAnnotation(permissions=['/ad-create/material'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editMaterialPacket(Input $input)
    {
        $input->verify(['media_type', 'id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->editMaterialPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取素材包列表
     * @param Input $input
     * @return array
     */
    public function getMaterialPacketList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getMaterialPacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取素材包info
     * @param Input $input
     * @return array
     */
    public function getMaterialPacketInfo(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getMaterialPacketInfo($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 获取文案包info
     * @param Input $input
     * @return array
     */
    public function getWordPacketInfo(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getWordPacketInfo($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 删除一个素材包
     * @CtrlAnnotation(permissions=['/ad-create/material'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteMaterialPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->deleteMaterialPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //----------------------------------------------------账号包--------------------------------------------------------

    /**
     * 新建一个账号包
     * @CtrlAnnotation(permissions=['/ad-create/account'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addAccountPacket(Input $input)
    {
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->addAccountPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '新建成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }

        return $response;
    }

    /**
     * 更新一个账号包
     * @CtrlAnnotation(permissions=['/ad-create/account'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editAccountPacket(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->editAccountPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取账号包列表
     * @param Input $input
     * @return array
     */
    public function getAccountPacketList(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getAccountPacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }


    /**
     * 删除一个账号包
     * @CtrlAnnotation(permissions=['/ad-create/account'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteAccountPacket(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->deleteAccountPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //-----------------------------------------------参数包-------------------------------------------------------------

    /**
     * 新建一个参数包
     * @CtrlAnnotation(permissions=['/ad-create/setting'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addSettingPacket(Input $input)
    {
        $input->verify(['media_type', 'name']);
        $ad_logic = new ADServingLogic();
        try {
            $add_data = $ad_logic->addSettingPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $add_data['state'] ? '新建成功' : '新建失败',
                'data' => [
                    'result' => $add_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 更新一个参数包
     * @CtrlAnnotation(permissions=['/ad-create/setting'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editSettingPacket(Input $input)
    {
        $input->verify(['id', 'name', 'media_type']);
        $ad_logic = new ADServingLogic();
        try {
            $edit_data = $ad_logic->editSettingPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $edit_data['state'] ? '修改成功' : '修改失败',
                'data' => [
                    'result' => $edit_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取参数包列表
     * @param Input $input
     * @return array
     */
    public function getSettingPacketList(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getSettingPacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 删除一个参数包
     * @CtrlAnnotation(permissions=['/ad-create/setting'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteSettingPacket(Input $input)
    {
        $input->verify(['id']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->deleteSettingPacket($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //--------------------------------------------------广告组合包---------------------------------------------------------

    public function getADComposePacketInfoById(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getADComposePacketInfoById($input->id);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 新建组合包
     * @param Input $input
     * @return array
     */
    public function getADComposePacketList(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getADComposePacketList($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 编辑组合包
     * @param Input $input
     * @return array
     */
    public function editADComposePacket(Input $input)
    {
        $input->verify(['id', 'media_type', 'name']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->editADComposePacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '更新成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 增加组合包
     * @param Input $input
     * @return array
     */
    public function addADComposePacket(Input $input)
    {
        $input->verify(['media_type', 'name']);
        $ad_logic = new ADServingLogic();
        try {
            $id = $ad_logic->addADComposePacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '新建成功',
                'data' => $id,
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除组合包
     * @param Input $input
     * @return array
     */
    public function deleteADComposePacket(Input $input)
    {
        $input->verify(['ids']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->deleteADComposePacket($input->getData()['ids']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //-----------------------------------------------广告任务-----------------------------------------------------------

    /**
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getTodayUnconsumeADTaskCount(Input $input)
    {
        $input->verify(['creator']);
        $data = (new ADTaskModel())->getTodayUnconsumeADTaskCount($input['creator']);
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => [
                'count' => $data ? $data->count : 0
            ],
            'message' => '组合成功',
        ];
    }

    /**
     * 保存广告组合并生成广告任务
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function ADComposeToPacketAndADTask(Input $input)
    {
        $input->verify(['media_type', 'name']);
        $ad_logic = new ADServingLogic();
        try {
            $result = $ad_logic->ADComposeToPacketAndADTask($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '组合成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 校验广告组合
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function ADComposeCheck(Input $input)
    {
        $input->verify(['media_type']);
        $ad_logic = new ADServingLogic();
        try {
            $data = $input->getData();
            $data['compose_id'] = $data['id'];
            $result = $ad_logic->ADComposeCheck($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '生成成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 生成广告任务
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function ADComposeToADTask(Input $input)
    {
        $input->verify(['media_type']);
        $ad_logic = new ADServingLogic();
        try {
            $data = $input->getData();
            $data['compose_id'] = $data['id'];
            $result = $ad_logic->ADComposeToADTask($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '生成成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 批量再次投放广告任务
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function againMakeADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->againMakeADTaskByIds($input['ids']);
            if ($result['state']) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_already_digest_num']}",
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_already_digest_num']}",
                ];
            }
        } catch (Throwable $e) {
            throw new AppException($e->getMessage());
        }
        return $response;
    }

    /**
     * 插队
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function jumpQueueADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $ad_logic->jumpQueueADTaskByIds($input['ids']);
        } catch (Exception $e) {
            throw new AppException($e->getMessage());
        }
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => "成功",
        ];
    }

    /**
     * 批量投放广告任务
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function restartADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->restartADTaskByIds($input['ids']);
            if ($result['state']) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_success_digest_num']}",
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_success_digest_num']}",
                ];
            }
        } catch (Exception $e) {
            throw new AppException($e->getMessage());
        }
        return $response;
    }

    /**
     * 批量启动广告任务
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function startADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->startADTaskByIds($input['ids']);
            if ($result['state']) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_success_digest_num']}",
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => "总共{$result['task_list_num']}条,成功执行{$result['task_success_digest_num']}",
                ];
            }
        } catch (Exception $e) {
            throw new AppException($e->getMessage());
        }
        return $response;
    }

    /**
     * 获取广告任务日志list
     * @param Input $input
     * @return array
     */
    public function getTaskLogList(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getTaskLogList(new ADTaskLogSearchParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取广告任务list
     * @param Input $input
     * @return array
     */
    public function getADTaskList(Input $input)
    {
//        throw new AppException('系统正在升级请稍后');
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getADTaskList(new ADTaskSearchParam($input->getData()));
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 删除广告任务(软删除)
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='delete')
     * @param Input $input
     * @return array
     */
    public function deleteADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->deleteADTaskByIds($input['ids']);
            if ($result) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => '删除成功',
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => '删除失败，此状态下的任务无法删除',
                ];
            }
        } catch (Exception $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 暂停广告任务
     * @CtrlAnnotation(permissions=['/ad-create/task'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function stopADTaskByIds(Input $input)
    {
        try {
            $input->verify(['ids']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->stopADTaskByIds($input['ids']);
            if ($result) {
                $response = [
                    'code' => ResponseCode::SUCCESS,
                    'message' => '暂停成功',
                ];
            } else {
                $response = [
                    'code' => ResponseCode::FAILURE,
                    'message' => '暂停失败，此状态下的任务无法暂停',
                ];
            }
        } catch (Exception $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取当前队列数量
     * @param Input $input
     * @return array
     */
    public function getQueueMessageNum(Input $input)
    {
        try {
            $input->verify(['media_type', 'platform']);
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->getQueueTaskNum($input['media_type'], $input['platform']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '查询成功',
            ];
        } catch (Exception $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //-------------------------------------------广告盒子------------------------------------------------------------

    /**
     * 校验广告盒子
     * @CtrlAnnotation(permissions=['/ad-create/ad-box'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function ADBoxCheck(Input $input)
    {
        array_map(function ($verify_data) use ($input) {
            $input->verify(
                ['id', 'platform', 'media_type', 'company', 'account_id', 'setting', 'other_setting', 'targeting', 'site_config', 'creative_list', 'compose_config'],
                true, $verify_data);
        }, $input->getData());

        $ad_logic = new ADServingLogic();
        try {
            $result = $ad_logic->ADBoxCheck($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '校验成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 广告盒子生成广告任务
     * @CtrlAnnotation(permissions=['/ad-create/ad-box'], log_type='add')
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function ADBoxToTask(Input $input)
    {
        array_map(function ($verify_data) use ($input) {
            $input->verify(
                ['id', 'platform', 'media_type', 'company', 'account_id', 'setting', 'other_setting', 'targeting', 'site_config', 'creative_list', 'compose_config'],
                true, $verify_data);
        }, $input->getData());

        $ad_logic = new ADServingLogic();
        try {
            $result = $ad_logic->ADBoxToTask($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'message' => '成功生成任务',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    //-------------------------------------------报表筛选代码------------------------------------------------------------

    /**
     * 报表指标列表
     * @CtrlAnnotation(permissions=['/ad-create/compose'])
     * @return array
     * @throws Exception
     */
    public function filterPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_COMPOSE);
        unset($data['素材属性']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 报表指标列表-素材包特殊接口
     * @CtrlAnnotation(permissions=['/ad-create/compose'])
     * @return array
     * @throws Exception
     */
    public function materialFileFilterPermission()
    {
        $data = (new PermissionLogic())->getADRoutePermission(RouteID::DSP_AD_COMPOSE);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 账号列表报表筛选
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getMediaAccountFilter(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getMediaAccountFilter($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 文案列表报表筛选
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getWordFilter(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getWordContentFilter($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 定向列表报表筛选
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getTargetingFilter(Input $input)
    {
        $ad_logic = new ADServingLogic();
        $data = $ad_logic->getTargetingFilter($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $data,
        ];
    }

    /**
     * 素材文件列表报表筛选
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='get')
     * @param Input $input
     * @return array
     */
    public function getMaterialFileFilter(Input $input)
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new ADServingLogic())->getMaterialFileFilter($input->getData()),
        ];
    }

    /**
     * 素材文件列表用户以及岗位列表
     * @CtrlAnnotation(permissions=['/ad-create/compose'])
     * @return array
     */
    public function getMaterialUserList()
    {
        $response = [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => (new MaterialLogic())->getUserList()
        ];
        return $response;
    }

    /**
     * 获取广告任务包状态
     * @param Input $input
     * @return array
     */
    public function getADTaskPackageStatus(Input $input)
    {
        $input->verify(['check_task_package_list']);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => [
                'list' => (new ADServingLogic())->getADTaskPackageStatus($input['check_task_package_list'])
            ],
        ];
    }

    //-------------------------------------------媒体特殊代码------------------------------------------------------------

    /**
     * 获取人群包列表
     * @param Input $input
     * @return array
     */
    public function getAudienceListByCondition(Input $input)
    {
        $input->verify(['media_type', 'company']);
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getAudienceListByCondition($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取人群包列表
     * @param Input $input
     * @return array
     */
    public function getFlowListByCondition(Input $input)
    {
        $input->verify(['media_type', 'page', 'rows']);
        $ad_logic = new ADServingLogic();
        $list_data = $ad_logic->getFlowListByCondition($input->getData());
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取行为类目
     * @param Input $input
     * @return array
     */
    public function getInterestActionCategoryList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_service = new MediaAD($input['media_type']);
        $list_data = $ad_service->getInterestActionCategoryList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取行为关键词
     * @param Input $input
     * @return array
     */
    public function getInterestActionKeywordList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_service = new MediaAD($input['media_type']);
        $list_data = $ad_service->getInterestActionKeywordList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取兴趣类目
     * @param Input $input
     * @return array
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_service = new MediaAD($input['media_type']);
        $list_data = $ad_service->getInterestInterestCategoryList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取兴趣关键词
     * @param Input $input
     * @return array
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_service = new MediaAD($input['media_type']);
        $list_data = $ad_service->getInterestInterestKeywordList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取创意行为列表
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        $input->verify(['media_type']);
        $ad_service = new MediaAD($input['media_type']);
        $list_data = $ad_service->getIndustryList($input);
        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list_data,
        ];
    }

    /**
     * 获取预估人数
     * @param Input $input
     * @return array
     */
    public function getTargetAudienceEstimateCount(Input $input)
    {
        $input->verify(['media_type', 'targeting']);
        try {
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->getTargetAudienceEstimateCount($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'msg' => '获取数据成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取任务错误方案映射列表
     * @param Input $input
     * @return array
     */
    public function getTaskSolutionList(Input $input)
    {
        $input->verify(['rows', 'page']);
        try {
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->getTaskSolutionList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'msg' => '获取数据成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取任务错误方案映射数据
     * @param Input $input
     * @return array
     */
    public function getTaskSolutionData(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $result = $ad_logic->getTaskSolutionData($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $result,
                'msg' => '获取数据成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 新增任务错误方案映射
     * @param Input $input
     * @return array
     */
    public function addTaskSolution(Input $input)
    {
        $input->verify(['media_type', 'question', 'solution']);
        try {
            $ad_logic = new ADServingLogic();
            $id = $ad_logic->addTaskSolution($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '新建成功',
                'data' => $id,
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 编辑任务错误方案映射
     * @param Input $input
     * @return array
     */
    public function editTaskSolution(Input $input)
    {
        $input->verify(['id', 'media_type', 'question', 'solution']);
        try {
            $ad_logic = new ADServingLogic();
            $resp = $ad_logic->editTaskSolution($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '修改成功',
                'data' => $resp,
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除任务错误方案映射
     * @param Input $input
     * @return array
     */
    public function deleteTaskSolution(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $resp = $ad_logic->deleteTaskSolution($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
                'data' => $resp,
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 记录组合时低效素材操作日志
     * @param Input $input
     * @return array
     */
    public function recordLowEffectAttemptLog(Input $input)
    {
        $input->verify(['media_type', 'game_id', 'platform']);
        try {
            $ad_logic = new ADServingLogic();
            $resp = $ad_logic->recordLowEffectAttemptLog($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '操作成功',
                'data' => $resp,
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 添加一个加热素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addHotMaterialPacket(Input $input)
    {
        $input->verify(['name', 'material_list']);
        try {
            $ad_logic = new ADServingLogic();
            $add_data = $ad_logic->addHotMaterialPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $add_data ? '新建成功' : '新建失败',
                'data' => [
                    'result' => $add_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 修改一个加热素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editHotMaterialPacket(Input $input)
    {
        $input->verify(['id', 'name', 'material_list']);
        try {
            $ad_logic = new ADServingLogic();
            $edit_data = $ad_logic->editHotMaterialPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $edit_data ? '修改成功' : '修改失败',
                'data' => [
                    'result' => $edit_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除加热素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function deleteHotMaterialPacket(Input $input)
    {
        $input->verify(['ids']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->deleteHotMaterialPacket($input->getData()['ids']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取加热素材包列表
     * @param Input $input
     * @return array
     */
    public function getHotMaterialPacketList(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getHotMaterialPacketList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 添加一个联投素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='add')
     * @param Input $input
     * @return array
     */
    public function addStarMaterialPacket(Input $input)
    {
        $input->verify(['name', 'package', 'material_list']);
        try {
            $ad_logic = new ADServingLogic();
            $add_data = $ad_logic->addStarMaterialPacket($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $add_data ? '新建成功' : '新建失败',
                'data' => [
                    'result' => $add_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 修改一个联投素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function editStarMaterialPacket(Input $input)
    {
        $input->verify(['id', 'name', 'material_list']);
        try {
            $ad_logic = new ADServingLogic();
            $edit_data = $ad_logic->editStarMaterialPacket($input['id'], $input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => $edit_data ? '修改成功' : '修改失败',
                'data' => [
                    'result' => $edit_data,
                ],
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 删除联投素材包
     * @CtrlAnnotation(permissions=['/ad-create/compose'], log_type='edit')
     * @param Input $input
     * @return array
     */
    public function deleteStarMaterialPacket(Input $input)
    {
        $input->verify(['ids']);
        $ad_logic = new ADServingLogic();
        try {
            $ad_logic->deleteStarMaterialPacket($input->getData()['ids']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '删除成功',
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function realTimeGetStarMaterial(Input $input)
    {
        $input->verify(['account_id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->realTimeGetStarMaterial($input['account_id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    /**
     * 获取联投素材包列表
     * @param Input $input
     * @return array
     */
    public function getStarMaterialPacketList(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getStarMaterialPacketList($input->getData());
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function webExecIntelligentCompose(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $ad_logic->webExecIntelligentCompose($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function previewIntelligentCompose(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->previewIntelligentCompose($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => [
                    'list' => $list_data,
                ],
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getADIntelligentComposeList(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getADIntelligentComposeList(new ADIntelligentComposeSearchParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getIntelligentComposeLog(Input $input)
    {
        $input->verify(['intelligent_compose_id']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getIntelligentComposeLog(new ADIntelligentComposeLogSearchParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function webPreviewIntelligentMonitorExecBodyConditionSql(Input $input)
    {
        $input->verify(['media_type', 'target_type', 'condition']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->webPreviewIntelligentMonitorExecBodyConditionSql(
                $input['media_type'],
                $input['target_type'],
                $input['condition']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function saveADIntelligentCompose(Input $input)
    {
        $input->verify(['id', 'name']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->saveADIntelligentCompose($input['id'], $input['name']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function stopIntelligentCompose(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->stopIntelligentCompose($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function startIntelligentCompose(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->startIntelligentCompose($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function updateADIntelligentCompose(Input $input)
    {
        $input->verify(['id', 'top_material_filter', 'potential_material_filter', 'new_material_filter']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->updateADIntelligentCompose($input['id'], new ADIntelligentComposeParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addADIntelligentCompose(Input $input)
    {
        $input->verify(['top_material_filter', 'potential_material_filter', 'new_material_filter']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->addADIntelligentCompose(new ADIntelligentComposeParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function deleteADIntelligentCompose(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->deleteADIntelligentCompose($input['id']);
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function previewIntelligentComposeMaterialFilter(Input $input)
    {
        $input->verify(['compose_id', 'filter_data_media_type', 'filter_content']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->previewIntelligentComposeMaterialFilter(
                $input['compose_id'],
                $input['filter_data_media_type'],
                $input['filter_content'],
                $input['filter_data_is_relate_root_game_id'] ?? 0,
                $input['filter_relate_root_game_id_list'] ?? [],
                $input['filter_data_is_relate_clique_game_id'] ?? 0,
                $input['filter_relate_clique_game_id_list'] ?? []
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function initIntelligentMonitorConfig()
    {
        return [
            'code' => ResponseCode::SUCCESS,
            'data' => (object)InitIntelligentMonitor::CONFIG,
            'message' => '成功'
        ];
    }

    public function getIntelligentMonitorExecBody(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getIntelligentMonitorExecBody(new ADIntelligentMonitorExecBodySearchParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addIntelligentMonitorExecBody(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->addIntelligentMonitorExecBody(
                new ADIntelligentMonitorExecBodyParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function updateIntelligentMonitorExecBody(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->updateIntelligentMonitorExecBody(
                $input['id'],
                new ADIntelligentMonitorExecBodyParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function deleteIntelligentMonitorExecBody(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->deleteIntelligentMonitorExecBody(
                $input['id'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getIntelligentMonitorRobot(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $list_data = $ad_logic->getIntelligentMonitorRobot(new ADIntelligentMonitorRobotSearchParam($input->getData()));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $list_data,
                'message' => '获取成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addIntelligentMonitorRobot(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->addIntelligentMonitorRobot(new ADIntelligentMonitorRobotParam($input));
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function updateIntelligentMonitorRobot(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->updateIntelligentMonitorRobot(
                $input['id'],
                new ADIntelligentMonitorRobotParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function deleteIntelligentMonitorRobot(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->deleteIntelligentMonitorRobot(
                $input['id'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getIntelligentComposeConditionPacket(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->getIntelligentComposeConditionPacket(
                new ADIntelligentComposeConditionSearchParam($input),
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function addIntelligentComposeConditionPacket(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->addIntelligentComposeConditionPacket(
                $input,
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function editIntelligentComposeConditionPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->editIntelligentComposeConditionPacket(
                $input['id'],
                $input,
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function deleteIntelligentComposeConditionPacket(Input $input)
    {
        $input->verify(['id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->deleteIntelligentComposeConditionPacket(
                $input['id'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getIntelligentMonitorBindLog(Input $input)
    {
        $input->verify(['monitor_robot_id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->getIntelligentMonitorBindLog(
                new ADIntelligentMonitorBindLogSearchParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function getIntelligentMonitorRobotLog(Input $input)
    {
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->getIntelligentMonitorRobotLog(
                new ADIntelligentMonitorRobotLogSearchParam($input)
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function bindRobotByTargetValueList(Input $input)
    {
        $input->verify(['robot_id', 'bind_type', 'target_type', 'target_value_list']);

        if ($input['bind_type'] == 'precise') {
            if (!isset($input['bind_target_type'])) {
                throw new AppException('bind_target_type不能为空');
            }
            if (!isset($input['bind_target_value_list'])) {
                throw new AppException('bind_target_value_list不能为空');
            }
        }
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->bindRobotByTargetValueList(
                $input['robot_id'],
                $input['bind_type'],
                $input['bind_target_type'] ?? '',
                $input['bind_target_value_list'] ?? '',
                $input['target_type'],
                $input['target_value_list'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function bindRobotByLogId(Input $input)
    {
        $input->verify(['log_id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->bindRobotByLogId(
                $input['log_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function unbindRobotByLogId(Input $input)
    {
        $input->verify(['log_id']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->unbindRobotByLogId(
                $input['log_id']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUnbindIntelligentMonitorBind(Input $input)
    {
        $input->verify(['ids']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUnbindIntelligentMonitorBind(
                $input['ids'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateIntelligentComposeCrateADWeek(Input $input)
    {
        $input->verify(['ids', 'weeks']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateIntelligentComposeCrateADWeek(
                $input['ids'],
                $input['weeks']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateIntelligentComposeCrateADTime(Input $input)
    {
        $input->verify(['ids', 'time']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateIntelligentComposeCrateADTime(
                $input['ids'],
                $input['time']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchStartADIntelligentCompose(Input $input)
    {
        $input->verify(['ids']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchStartADIntelligentCompose(
                $input['ids']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchEndADIntelligentCompose(Input $input)
    {
        $input->verify(['ids']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchEndADIntelligentCompose(
                $input['ids']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateCustomMaterialPacket(Input $input)
    {
        $input->verify(['ids']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateCustomMaterialPacket(
                $input['ids'],
                $input['custom_material_packet_list'] ?? []
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateIntelligentComposeMaterialComposeType(Input $input)
    {
        $input->verify(['ids', 'material_compose_type']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateIntelligentComposeMaterialComposeType(
                $input['ids'],
                $input['material_compose_type']
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchDeleteADIntelligentCompose(Input $input)
    {
        $input->verify(['ids']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchDeleteADIntelligentCompose(
                $input['ids'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateMaterialWordNum(Input $input)
    {
        $input->verify(['ids','material_num','word_num']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateMaterialWordNum(
                $input['ids'],
                $input['material_num'],
                $input['word_num'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }

    public function batchUpdateDispatchType(Input $input)
    {
        $input->verify(['ids','dispatch_type']);
        try {
            $ad_logic = new ADServingLogic();
            $data = $ad_logic->batchUpdateDispatchType(
                $input['ids'],
                $input['dispatch_type'],
            );
            $response = [
                'code' => ResponseCode::SUCCESS,
                'data' => $data,
                'message' => '成功'
            ];
        } catch (AppException $e) {
            $response = [
                'code' => ResponseCode::FAILURE,
                'data' => [],
                'message' => $e->getMessage()
            ];
        }
        return $response;
    }
}
