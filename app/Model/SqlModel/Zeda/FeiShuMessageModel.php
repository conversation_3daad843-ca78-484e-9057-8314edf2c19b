<?php

namespace App\Model\SqlModel\Zeda;

use Illuminate\Support\Collection;

class FeiShuMessageModel extends AbstractZedaSqlModel
{
    protected $table = 'feishu_message';


    public function addOne($data)
    {
        return $this->builder->insertGetId([
            'message_id'          => $data['message_id'],
            'chat_id'             => $data['chat_id'],
            //            'decrypt'             => $data['decrypt'],
            'decrypt'             => json_encode([]),
            'format_message'      => $data['format_message'],
            'message_create_time' => date("Y-m-d H:i:s", $data['message_create_time']),
            'msg_type'            => $data['msg_type'],
            'parent_id'           => $data['parent_id'] ?? '',
            'union_id'            => $data['union_id'] ?? '',
        ]);
    }

    public function replaceOne($data)
    {
        return $this->builder->replace([
            'message_id'          => $data['message_id'],
            'chat_id'             => $data['chat_id'],
            'decrypt'             => json_encode([]),
            'format_message'      => $data['format_message'],
            'message_create_time' => date("Y-m-d H:i:s", $data['message_create_time']),
            'msg_type'            => $data['msg_type'],
            'union_id'            => $data['union_id'] ?? '',
            'parent_id'           => $data['parent_id'] ?? '',
            'cut_off'             => $data['cut_off'] ?? 1,
            'at'                  => $data['at'] ?? 0,
        ]);
    }

    public function updateMessage($message_id, $format_message, $cut_off)
    {
        return $this->builder->where('message_id', $message_id)->update([
            'format_message' => $format_message,
            'cut_off'        => $cut_off,
        ]);
    }


    public function updateState($message_id, $state)
    {
        return $this->builder->where('message_id', $message_id)->update(['state' => $state]);
    }

    public function updateReactionTypeStateByMessageID($message_id)
    {
        return $this->builder
            ->where('parent_id', $message_id)
            ->where('msg_type', '=', 'emoji_reaction')
            ->where('state', '=', 1)
            ->update(['state' => 0]);
    }

    public function updateReactionTypeState($message_id, $union_id)
    {
        return $this->builder
            ->where('parent_id', $message_id)
            ->where('msg_type', '=', 'emoji_reaction')
            ->where('state', '=', 1)
            ->where('union_id', '=', $union_id)
            ->update(['state' => 0]);
    }

    /**
     * 返回指定时间范围内的消息，注意这里的时间边界
     *
     * @param $chat_id
     * @param $start_time
     * @param $end_time
     * @param int $limit
     * @param string $union_id
     * @return Collection
     */
    public function getListByChatId($chat_id, $start_time, $end_time, int $limit = 1000, string $union_id = '')
    {
        if (is_array($chat_id)) {
            $builder = $this->builder->whereIn('chat_id', $chat_id);
        } else {
            $builder = $this->builder->where('chat_id', $chat_id);
        }
        if ($union_id) {
            $builder->where('union_id', $union_id);
        }
        $builder->where('state', 1)->orderBy('message_create_time', 'DESC');
        if ($limit) {
            $builder->limit($limit);
        }
        if ($start_time) {
            $builder->where('message_create_time', '>=', $start_time);
        }
        if ($end_time) {
            $builder->where('message_create_time', '<', $end_time);
        }
        return $builder->get()->sortBy('message_create_time')->values();
    }

    /**
     * 获取message_id
     *
     * @param $chat_id
     * @param $start_time
     * @param $end_time
     * @param int $limit
     * @return array
     */
    public function getMessageIDByChatId($chat_id, $start_time, $end_time, int $limit = 1000)
    {
        if (is_array($chat_id)) {
            $builder = $this->builder->whereIn('chat_id', $chat_id);
        } else {
            $builder = $this->builder->where('chat_id', $chat_id);
        }
        $builder->where('state', 1)->orderBy('message_create_time', 'DESC');
        if ($limit) {
            $builder->limit($limit);
        }
        if ($start_time) {
            $builder->where('message_create_time', '>=', $start_time);
        }
        if ($end_time) {
            $builder->where('message_create_time', '<', $end_time);
        }
        return $builder->get(['message_id'])->pluck('message_id')->toArray();
    }

    public function getListByChatIdAndTime($chat_id, $start_time, $end_time, $limit = 100)
    {
        return $this->builder->where('chat_id', $chat_id)
            ->whereBetween('message_create_time', [$start_time, $end_time])
            ->where('state', 1)
            ->orderBy('message_create_time', 'DESC')
            ->limit($limit)
            ->get()->sortBy('message_create_time')->values();
    }

    public function getListByUnionAndTime($union_id, $start_time, $end_time, $limit = 100)
    {
        return $this->builder->where('union_id', $union_id)
            ->whereBetween('message_create_time', [$start_time, $end_time])
            ->where('state', 1)
            ->where('chat_id', '=', '0')
            ->orderBy('message_create_time', 'DESC')
            ->limit($limit)
            ->get()->sortBy('message_create_time')->values();
    }

    /**
     * @param array $message_ids
     * @param  $at_filter
     * @return Collection
     */
    public function getListByMessageIds(array $message_ids, $at_filter = null)
    {
        $builder = $this->builder
            ->whereIn('message_id', $message_ids)
            ->where('state', 1)
            ->orderBy('message_create_time', 'ASC');

        if ($at_filter !== null) {
            $builder->where('at', $at_filter);
        }

        return $builder->get();
    }


    public function getListByChatIdAndLastTime($chat_id, $last_time, int $limit = 10000)
    {
        $builder = $this->builder->where('chat_id', $chat_id)
            ->orderBy('message_create_time', 'ASC')
            ->where('message_create_time', '>=', $last_time)
            ->where('state', 1);
        if ($limit) {
            $builder->limit($limit);
        }
        return $builder->get();
    }

    public function getListByUnionIdAndLastTime($union_id, $last_time, int $limit = 10000)
    {
        $builder = $this->builder->where('union_id', $union_id)
            ->where('message_create_time', '>=', $last_time)
            ->where('chat_id', '=', '0')
            ->orderBy('message_create_time', 'ASC')
            ->where('state', 1);
        if ($limit) {
            $builder->limit($limit);
        }
        return $builder->get();
    }


    public function getChatListByMessageIds(array $message_ids)
    {
        $builder = $this->builder->from('feishu_assistant_bot_chat');
        $sub_builder = $this->builder->whereIn('message_id', $message_ids)->select('chat_id')->groupBy('chat_id');
        $builder->whereIn('chat_id', $sub_builder);

        return $builder->get();
    }

    public function getDetailByMessageId($message_id)
    {
        return $this->builder->where('message_id', $message_id)->where('state', 1)->first();
    }
}