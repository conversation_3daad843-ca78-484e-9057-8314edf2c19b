<?php

namespace App\Model\SqlModel\Zeda;

class FeishuMessageSummary extends AbstractZedaSqlModel
{

    protected $table = 'feishu_message_summary';

    // 字段 id 总结内容  chat_id(群id)  消息id列表 总结开始时间 总结结束时间 inset_time


    /**
     * 总结的消息要入表
     *
     * @param $data
     * @return int
     */
    public function addOne($data)
    {
        $insert_data = [
            'summary'     => $data['summary'],
            'chat_id'     => $data['chat_id'],
            'msg_id_list' => json_encode($data['msg_id_list']),
            'start_time'  => $data['start_time'],
            'end_time'    => $data['end_time'],
            'type'        => $data['type'] ?? 0,
        ];

        if ($data['entity']) {
            $insert_data['entity'] = $data['entity'];
        }
        if ($data['question']) {
            $insert_data['question'] = $data['question'];
        }
        return $this->builder->insertGetId($insert_data);
    }

    public function getData($id)
    {
        return $this->builder->where('id', $id)->first();
    }

    public function getLastSummaryContent($chat_id, $summary_id = 0)
    {
        $builder = $this->builder->where('chat_id', $chat_id)->where('type', 1)->orderBy('end_time', 'DESC');
        if ($summary_id > 0) {
            $builder->where('id', '<', $summary_id);
        }
        return $builder->first();
    }

    public function getListByIds(array $ids)
    {
        return $this->builder->whereIn('id', $ids)->get();
    }

    public function updateSummary($id, $summary, $entity, $question)
    {
        $data = [
            'summary'  => $summary,
            'entity'   => $entity,
            'question' => $question,
        ];

        $this->builder->where('id', $id)->update($data);
    }
}