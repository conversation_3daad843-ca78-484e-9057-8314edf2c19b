<?php


namespace App\Model\RedisModel;


class StarDemandToBpmModel extends AbstractRedisModel
{
    const KEY = 'STAR_DEMAND_TO_BPM';
    const LOCK_TIME = 10 * 60;

    public function lock($type, $lock_time = self::LOCK_TIME)
    {
        $key = $this->genLock<PERSON>ey($type);
        return $this->redis->set($key, '1', ['nx', 'ex' => $lock_time]);
    }

    public function unLock($type)
    {
        $key = $this->genLock<PERSON>ey($type);
        $this->redis->del($key);
    }

    private function genLockKey($type)
    {
        return self::KEY . "{$type}_LOCK";
    }
}
