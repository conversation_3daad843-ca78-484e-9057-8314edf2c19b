<?php
/**
 * openAI的参数对象
 * 如有缺失的参数后续可以自行补充
 */

namespace App\Param;

use App\Model\HttpModel\OpenAI\OpenAIDeepSeekModel;

class OpenAIParam extends AbstractParam
{

    /**
     * 模型 默认用DeepSeek的R1模型
     *
     * @var string
     */
    public $model = OpenAIDeepSeekModel::DOUBAO_MODEL;


    /**
     * 到目前为止的对话组成的消息列表
     * @var array
     */
    public $messages = [];


    /**
     * 流式响应的选项。
     *
     * @var bool
     */
    public $stream = false;


    /**
     * 模型遇到 stop 字段所指定的字符串时将停止继续生成，这个词语本身不会输出。最多支持 4 个字符串。
     *
     * @var mixed
     */
    public $stop = null;


    /**
     * 模型输出内容须遵循此处指定的格式
     *
     * @var string[]
     */
    public $response_format = [
        'type' => 'text',
    ];


    /**
     * 取值范围为 [-2.0, 2.0]。
     * 频率惩罚系数。如果值为正，会根据新 token 在文本中的出现频率对其进行惩罚，从而降低模型逐字重复的可能性。
     *
     * @var float
     */
    public $frequency_penalty = 0;


    /**
     * 取值范围为 [-2.0, 2.0]。
     * 存在惩罚系数。如果值为正，会根据新 token 到目前为止是否出现在文本中对其进行惩罚，从而增加模型谈论新主题的可能性
     * @var float
     */
    public $presence_penalty = 0;


    /**
     *  取值范围为 [0, 2]。Œ
     *  采样温度。控制了生成文本时对每个候选词的概率分布进行平滑的程度。当取值为 0 时模型仅考虑对数概率最大的一个 token。
     *  较高的值（如 0.8）会使输出更加随机，而较低的值（如 0.2）会使输出更加集中确定。
     *  通常建议仅调整 temperature 或 top_p 其中之一，不建议两者都修改。
     *
     * @var float
     */
    public $temperature = 1;


    /**
     * 取值范围为 [0, 1]。
     * 核采样概率阈值。模型会考虑概率质量在 top_p 内的 token 结果。当取值为 0 时模型仅考虑对数概率最大的一个 token。
     * 0.1 意味着只考虑概率质量最高的前 10% 的 token，取值越大生成的随机性越高，取值越低生成的确定性越高。通常建议仅调整 temperature 或 top_p 其中之一，不建议两者都修改。
     * @var float
     */
    public $top_p = 0.7;


    /**
     * 待调用工具的列表，模型返回信息中可包含。当您需要让模型返回待调用工具时，需要配置该结构体。支持该字段的模型请参见文档
     * @see https://www.volcengine.com/docs/82379/1330310#%E5%B7%A5%E5%85%B7%E8%B0%83%E7%94%A8%E8%83%BD%E5%8A%9B
     * @var array
     */
    public $tools = [];


    /**
     * 本次请求，模型返回信息中是否有待调用的工具。
     * 当没有指定工具时，none 是默认值。如果存在工具，则 auto 是默认值
     *
     * @var string
     */
    public $tool_choice = "auto";


    /****************************************** 以下是本地参数，非远程调用时的参数 *****************************************/

    /**
     * 项目名称，
     *
     * @var string
     */
    public $project = '';

    /**
     * 类型
     *
     * @var string
     */
    public $type = '';

    /**
     * 调用者的身份
     *
     * @var string
     */
    public $username = '';


    /**
     * 如果是流式调用，还需要传入一个回调函数
     *
     * @var callable
     */
    public $callback = null;


    /**
     * 是否记录日志
     * 如果需要记录。直接传入需要记录的字符串
     *
     * @var bool|string
     */
    public $log = false;

}