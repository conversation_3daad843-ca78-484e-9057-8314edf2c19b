<?php

namespace App\Service\GroupAssistant;

use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\CardKit\CardModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Volcengine\Knowledge\AbstractKnowledgeModel;
use App\Model\HttpModel\Volcengine\Knowledge\CollectionModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantBotChatModel;
use App\Model\SqlModel\Zeda\FeiShuMessageDocsModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\Model\SqlModel\Zeda\FeishuUserChatModel;
use App\Model\SqlModel\Zeda\FeiShuUserModel;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Param\OpenAIParam;
use App\Service\DataBot\DataBotConfig;
use App\Service\DataBot\DataBotService;
use App\Service\FeiShuService;
use App\Service\OpenAI\OpenAI;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use DateTime;
use Exception;
use Illuminate\Support\Collection;
use Monolog\Logger;
use RedisException;

/**
 * 知识问答
 */
class KnowledgeService
{

    // 1. 接受消息
    // 2. 检索
    // 3. 拼接
    // 4. LLM问答
    // 5. 返回给飞书
    const QA_SESSION_KEY = 'knowledge_answer_';

    const SESSION_EXPIRE_TIME = 86400;

    const SESSION_MAX_LENGTH_STR = 300;

    /**
     * 公共文档库的chat_id
     */
    const COMMON_CHAT_ID = 'oc_a49a79eb7fcebd57a17ead72c71884de';

    /**
     * @var Logger
     */
    public $logger;

    public function __construct()
    {
        $this->logger = Helpers::getLogger('group_assistant_qa');
    }


    /**
     * 知识问答
     *
     * @throws RedisException|Exception
     */
    public function getKnowledgeAnswer(KnowledgeParam $param, FeishuStreamCardParam $message_param)
    {
        $this->logger->info('用户提问：' . $param->query . '。问答开始。', ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
        $message_param->setContent('正在思考，请稍后....');
        // 发个消息让用户等待一下
        FeiShuService::stream($message_param);

        // 找到用户所在的群列表
        $param->chat_list = $this->getUserChatList($param);

        // 向量召回（去知识库检索）
        $chunk_list = $this->knowledgeVectorRecall($param);

        // 对召回的结果的分数进行时间加权重新计算，然后重排，重新获取到summary_id
        $summary_id_list = $this->reRank($chunk_list);

        $this->logger->info("用户提问: {$param->query}。知识检索完成", ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);


        // 获取消息列表
        [$message_list, $message_id_list] = $this->getMessageList($param, $summary_id_list);


        /*** @var $message_list Collection */
        if ($message_list->isEmpty()) {
            $this->logger->info("用户提问: {$param->query}。 没有任何的消息背景，无法回答用户提问。", ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
            $message_param->setContent('没有任何的消息背景，无法回答用户提问。');
            FeiShuService::stream($message_param);
            return;
        }

        // 文档检索,替换消息里面的文档内容
        $this->docSearch($param, $message_id_list, $message_list);

        // 格式化消息列表
        $this->formatMessageList($param, $message_list);

        // 多轮对话问答
        $this->chatCompletion($param, $message_param);


        $this->logger->info('用户消息回复完成', ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
    }


    private function getUserChatList(KnowledgeParam $param)
    {
        if ($param->type === 'p2p') {
            // 找到用户所在的群id
            $chat_list = (new FeishuUserChatModel())->getChatListByUnionId($param->union_id);
            // 转换成数组
            $chat_list = $chat_list->pluck('chat_id')->toArray();
            $this->logger->info("用户提问: {$param->query}。开始检索。用户所在群列表：", ['chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'chat_list' => $chat_list]);
        } else {
            $this->logger->info("用户提问: {$param->query}。开始检索。用户指定群列表：", ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
            $chat_list = [$param->chat_id];
        }

        return $chat_list;
    }


    /**
     * 去搜索知识库，向量检索
     *
     * @param KnowledgeParam $param
     * @return array
     * @throws Exception
     */
    private function knowledgeVectorRecall(KnowledgeParam $param)
    {
        // 先去获取参数
        [$pre_processing, $post_processing] = $this->getKnowledgeVectorRecallParam($param);

        // 检索结果列表
        $respond_list = [];
        // 检索切片, 要多路召回，分内容、实体、提问三路召回
        $chunk_list = [];
        // 召回的summary_id池,用来去重
        $recall_summary_id_pool = [];
        foreach (['summary_content', 'summary_entity', 'summary_question'] as $item) {
            $doc_filter = $this->getDocFilter($param, $item);
            if ($item === 'summary_entity') {
                $dense_weight = 0.2;
            } else {
                $dense_weight = 0.5;
            }
            $respond = (new CollectionModel())->searchKnowledge(AbstractKnowledgeModel::SUMMARY_RESOURCE_ID, $param->query, $doc_filter, $pre_processing, 10, $dense_weight, $post_processing);

            // 保存debug信息
            $respond_list[$item] = ['filter' => $doc_filter, 'respond' => $respond];
            $this->logger->info("用户提问: {$param->query}。{$item}检索结果： ", ['doc_filter' => $doc_filter, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'respond' => $respond]);

            // 检索到了切片
            if ($respond['code'] == 0 && $respond['data']['count'] > 0) {
                foreach ($respond['data']['result_list'] as $chunk) {
                    // 设置一个阈值 低于0.2的不需要
                    if ($chunk['rerank_score'] < 0.2) {
                        continue;
                    }
                    // 提取切片里面的id（总结id)
                    preg_match('/<id>(.*?)<id>/', $chunk['content'], $matches);
                    if (!empty($matches[1]) && !in_array($matches[1], $recall_summary_id_pool)) {
                        $chunk_list[$item][] = ['summary_id' => $matches[1], 'score' => $chunk['rerank_score']];
                        // 存入池子，去重
                        $recall_summary_id_pool[] = $matches[1];
                    }
                }
            }
        }

        // 保留debug信息
        $param->respond = $respond_list;

        return $chunk_list;
    }


    /**
     * 获取知识库检索的参数
     *
     * @param KnowledgeParam $param
     * @return array
     * @throws Exception
     */
    private function getKnowledgeVectorRecallParam(KnowledgeParam $param)
    {
        // 获取历史对话内容
        $rewrite_messages = $this->getRewriteRequestParam($param->session_key);

        // 检索预处理参数 改写非首轮问题
        $pre_processing = [
            'need_instruction' => true,
            'rewrite'          => true,
            'messages'         => $rewrite_messages,
        ];

        // 检索后处理参数 重排切片
        $post_processing = [
            'rerank_switch' => true,
            'chunk_group'   => true, // 文本聚合
            'rerank_model'  => 'base-multilingual-rerank',//速度快、长文本、支持70+种语言（推荐）
        ];

        return [$pre_processing, $post_processing];
    }

    /**
     * 文档检索，如果存在文档的消息，则替换消息里面的文档内容
     * 消息里面的云文档内容是总结过的内容，而检索出来的是完整内容，所以需要替换
     * 原消息里面会用文档id标签把内容包起来，方便替换。
     * 例如原消息内容：<docx_VljtdShVfojZwkxGmKtcYcj7nUo>这里是示例的文档总结内容</docx_VljtdShVfojZwkxGmKtcYcj7nUo>
     * 替换成：<docx_VljtdShVfojZwkxGmKtcYcj7nUo>这里是示例的文档完整内容</docx_VljtdShVfojZwkxGmKtcYcj7nUo>
     * 然后再把标签也替换掉，最终变成：
     * “这里是示例的文档完整内容”
     *
     * @param KnowledgeParam $param
     * @param $message_id_list
     * @param Collection $message_list
     * @return void
     * @throws Exception
     */
    private function docSearch(KnowledgeParam $param, $message_id_list, Collection $message_list)
    {
        $logger = Helpers::getLogger('group_assistant_qa');
        // 获取消息id对应的云文档id
        [$doc_list, $doc_message_list] = $this->getDocListByMessageId($message_id_list);

        /**
         * @var $doc_list Collection
         * @var $doc_message_list Collection
         */

        // 空 ，直接返回，不处理
        if ($doc_list->isEmpty()) {
            return;
        }
        $condition_list = [];
        foreach ($doc_list as $item) {
            $condition_list[] = $item->doc_type . '_' . $item->doc_token;
        }

        // 检索文档库
        $doc_filter = [
            'op'    => 'must',
            'field' => 'doc_id',
            'conds' => $condition_list,
        ];
        // 获取检索参数
        [$pre_processing, $post_processing] = $this->getKnowledgeVectorRecallParam($param);
        $respond = (new CollectionModel())->searchKnowledge(AbstractKnowledgeModel::DOC_RESOURCE_ID, $param->query, $doc_filter, $pre_processing, 10, 0.5, $post_processing);

        $logger->info("用户提问: {$param->query}。云文档的检索结果： ", ['doc_filter' => $doc_filter, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'respond' => $respond]);

        // 检索到了切片, 把文档内容跟id提取出来，做成映射关系
        $doc_content = [];
        if ($respond['code'] == 0 && $respond['data']['count'] > 0) {
            foreach ($respond['data']['result_list'] as $chunk) {
                $doc_id = $chunk['doc_info']['doc_id'];
                $doc_content[$doc_id][] = $chunk['content'];
            }
        }
        if (empty($doc_content)) {
            $logger->info("用户提问: {$param->query}。无文档", ['doc_filter' => $doc_filter, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'respond' => $respond]);
            return;
        }


        // 找到需要替换的消息id
        $replace_list = $doc_message_list->filter(function ($item) use ($doc_content) {
            $doc_id = $item->doc_type . '_' . $item->doc_token;
            return isset($doc_content[$doc_id]);
        })->pluck('message_id')->toArray();


        $index_list = $doc_message_list->groupBy('message_id');

        // 存放替换过的文档
        $replace_pool = [];

        // 处理消息替换
        foreach ($message_list as $message_item) {
            if (!in_array($message_item->message_id, $replace_list)) {
                continue;
            }
            if (!isset($index_list[$message_item->message_id])) {
                continue;
            }
            $format_message = json_decode($message_item->format_message, true);
            // 一个消息可能有多个需要替换的文档内容
            foreach ($index_list[$message_item->message_id] as $item) {
                $doc_id = $item->doc_type . '_' . $item->doc_token;


                // docx_DsvodyWaZojcS4xPEHLc4gY2nsd

                // 使用正则表达式匹配特定标签内的内容 这里的标签用的就是doc_id
                $pattern = "/(<$doc_id>)(.*?)(<\/$doc_id>)/s";

                // 替换
                $new_content = implode("\n", $doc_content[$doc_id]);
                $new_content = '（KBTable标签为表格识别出来的内容，KBImage为图片OCR识别的内容，其中图片识别重复出现的文字可能是水印，请忽略处理）' . $new_content;


                // 判断msg_type
                if ($format_message['msg_type'] === 'text' || $format_message['msg_type'] === 'file') {
                    $format_message['content'] = preg_replace($pattern, "$new_content", $format_message['content']);
                    $logger->info("用户提问: {$param->query}。文档替换成功", ['message_id' => $message_item->message_id, 'doc_id' => $doc_id, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'new_content' => $new_content]);
                } elseif ($format_message['msg_type'] === 'post') {
                    $content = json_decode($format_message['content'], true);

                    foreach ($content['content'] as &$items) {
                        if (empty($items)) {
                            continue;
                        }
                        foreach ($items as &$item) {
                            if ($item['tag'] === 'text') {
                                $item['text'] = preg_replace($pattern, "$new_content", $format_message['content']);
                                $logger->info("用户提问: {$param->query}。文档替换成功", ['message_id' => $message_item->message_id, 'doc_id' => $doc_id, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id, 'new_content' => $new_content]);

                            }
                            // 其他类型原样保留
                        }
                    }
                    $format_message['content'] = json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                }

            }
            $message_item->format_message = json_encode($format_message, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }


        // 处理消息替换 把多余的标签替换掉
        foreach ($message_list as $message_item) {
            if (!isset($index_list[$message_item->message_id])) {
                continue;
            }
            $format_message = json_decode($message_item->format_message, true);

            // 使用正则表达式匹配特定标签内的内容 这里的标签用的就是doc_id
            $pattern = '/<\s*(docx|doc|sheet|bitable)_[^>]+>(.*?)<\/\s*\1_[^>]+>/is';


            // 判断msg_type
            if ($format_message['msg_type'] === 'text') {
                $format_message['content'] = preg_replace($pattern, '$2', $format_message['content']);
                $logger->info("用户提问: {$param->query}。标签替换成功", ['message_id' => $message_item->message_id, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
            } elseif ($format_message['msg_type'] === 'post') {
                $content = json_decode($format_message['content'], true);

                foreach ($content['content'] as &$items) {
                    if (empty($items)) {
                        continue;
                    }
                    foreach ($items as &$item) {
                        if ($item['tag'] === 'text') {
                            $item['text'] = preg_replace($pattern, '$2', $format_message['content']);
                            $logger->info("用户提问: {$param->query}。标签替换成功", ['message_id' => $message_item->message_id, 'chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
                        }
                    }
                }
                $format_message['content'] = json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }


            $message_item->format_message = json_encode($format_message, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }


    }

    /**
     * @param KnowledgeParam $param
     * @param Collection $message_list
     * @return void
     */
    private function formatMessageList(KnowledgeParam $param, Collection $message_list)
    {
        // 需要按群分类
        $chat_message_list = $message_list->groupBy('chat_id');
        $result = '';
        $limit = ceil(100000 / $chat_message_list->count()); // 按群的数量分配限额
        // chat_id做一个映射，不然大模型会瞎几把返回这个id
        $chat_id_map = [];
        // URL映射，防止大模型产生URL幻觉
        $url_map = [];
        $url_index = 1;
        $index = 1;
        foreach ($chat_message_list as $chat_id => $chat_message) {
            $message_format_list = GroupAssistantService::formatToLLMMessageList($chat_message, $limit, 5000, 10000);

            // 提取并替换消息中的URL
            $message_format_list = $this->extractAndReplaceUrls($message_format_list, $url_map, $url_index);

            $chat_id_map[$index] = strval($chat_id); // chat_id一定是字符串，“0” 也是字符串
            $result .= "
<chat>
<chat_id>{$index}</chat_id>
<消息记录>$message_format_list</消息记录>
</chat>";
            $index++;
        }

        // 把公共知识库也map进来
        $chat_id_map[$index] = self::COMMON_CHAT_ID;

        $param->message_list = $result;
        $param->chat_id_map = $chat_id_map;
        $param->url_map = $url_map;
    }

    /**
     * 提取并替换消息中的URL，防止大模型产生URL幻觉
     *
     * @param string $message_content
     * @param array &$url_map
     * @param int &$url_index
     * @return string
     */
    private function extractAndReplaceUrls(string $message_content, array &$url_map, int &$url_index)
    {
        // 匹配各种URL格式的正则表达式
        $url_patterns = [
            // HTTP/HTTPS URLs
            '/https?:\/\/[^\s\[\]()]+/i',
            // Markdown链接格式 [text](url)
            '/\[([^\]]*)\]\((https?:\/\/[^\s\[\]()]+)\)/i',
            // 飞书文档链接等特殊格式
            '/(?:https?:\/\/)?[a-zA-Z0-9][\w\-]*\.(?:feishu|lark)\.cn\/[^\s\[\]()]+/i'
        ];

        foreach ($url_patterns as $pattern) {
            $message_content = preg_replace_callback($pattern, function ($matches) use (&$url_map, &$url_index) {
                $full_match = $matches[0];
                $url = '';
                $text = '';

                // 处理Markdown链接格式
                if (isset($matches[2])) {
                    $text = $matches[1];
                    $url = $matches[2];
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => $text, 'original' => $full_match];
                    $url_index++;
                    return "[{$text}]({$replacement_key})";
                } else {
                    // 处理普通URL
                    $url = $full_match;
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => '', 'original' => $full_match];
                    $url_index++;
                    return $replacement_key;
                }
            }, $message_content);
        }

        return $message_content;
    }

    /**
     * 将大模型回答中的URL映射替换回真实URL
     *
     * @param string $content
     * @param array $url_map
     * @return string
     */
    public function replaceUrlMappings(string $content, array $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            // 处理Markdown链接格式的替换
            if (!empty($url_info['text'])) {
                // 替换 [text](URL_X) 格式
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }

            // 处理普通URL替换
            $content = str_replace($replacement_key, $url_info['url'], $content);
        }

        return $content;
    }

    /**
     * 流式输出中的URL替换处理，考虑不完整字符串的情况
     *
     * @param string $chunk 新的内容块
     * @param string &$buffer 缓冲区，用于处理跨chunk的URL标识
     * @param array $url_map URL映射表
     * @return string 可以安全输出的内容
     */


    /**
     * 处理Markdown链接格式的流式替换
     *
     * @param string $content 内容
     * @param array $url_map URL映射表
     * @return string
     */


    /**
     * 获取流式输出安全内容，避免输出不完整的URL标识
     *
     * @param string $content 完整内容
     * @param array $url_map URL映射表
     * @return string 安全的输出内容
     */
    private function getSafeContentForStreaming($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        // 检查内容末尾是否有不完整的URL标识
        $safe_content = $content;

        // 检查是否以不完整的URL标识结尾
        if (preg_match('/URL_?\d*$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_start = $matches[0][1];
            $incomplete_part = $matches[0][0];

            // 如果是不完整的URL标识，暂时不输出这部分
            if (!isset($url_map[$incomplete_part])) {
                $safe_content = substr($content, 0, $match_start);
            }
        }

        // 检查是否有不完整的Markdown链接
        if (preg_match('/\[[^\]]*\]\(URL_?\d*$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_start = $matches[0][1];
            $safe_content = substr($content, 0, $match_start);
        }

        // 对安全内容进行URL替换
        $safe_content = $this->replaceUrlMappings($safe_content, $url_map);

        return $safe_content;
    }

    /**
     * 多轮对话
     *
     * @param KnowledgeParam $param
     * @param FeishuStreamCardParam $message_param
     * @throws RedisException|Exception
     */
    private function chatCompletion(KnowledgeParam $param, FeishuStreamCardParam $message_param)
    {
        $logger = Helpers::getLogger('group_assistant_qa');
        $request_content = $this->getRequestParam($param->query, $param->message_list, $param->session_key, $param->union_id);
        $logger->info("用户提问: {$param->query}。开始去询问大模型", ['chat_id' => $param->chat_id, 'union_id' => $param->union_id]);
        $push_content = '';   // 未处理过的原始push内容
        $buffer = '';
        $main_content_buffer = '';
        $is_id_extracted = false;
        $chat_id_content = '';
        $start_timestamp = microtime(true) * 1000;// 微秒时间戳

        // 流式输出的处理函数
        $request_content['callback'] = function ($data) use (&$start_timestamp, &$push_content, $param, $message_param, &$buffer, &$is_id_extracted, &$chat_id_content, &$main_content_buffer) {
            $chunk = $data['content'];
            $access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
            $message_param->tenant_access_token = $access_token;

            if (!$is_id_extracted) {
                // 累积到缓冲区，把消息id提取完了再开始发送
                $buffer .= $chunk;

                // 尝试匹配消息ID标签
                $pattern = '/<引用的chat_id>(.*?)<\/引用的chat_id>/s';
                if (preg_match($pattern, $buffer, $matches, PREG_OFFSET_CAPTURE)) {
                    $chat_id_content = $matches[1][0]; // 提取消息ID内容
                    $end_pos = intval($matches[0][1]) + strlen($matches[0][0]);
                    $remaining_content = substr($buffer, $end_pos);

                    // 去掉消息标签后的内容，存入缓冲区和总内容
                    $push_content .= $remaining_content; // 累积完整内容
                    $main_content_buffer .= $remaining_content;
                    $is_id_extracted = true;
                    $buffer = ''; // 清空消息id的缓冲区
                }
            } else {
                $push_content .= $chunk; // 累积到完整内容
                $main_content_buffer .= $chunk; // 内容缓冲区
                $now = microtime(true) * 1000;
                // 每300毫秒输出一次，并且判断一下字符数是否大于10
                if ($now - $start_timestamp > 300 && strlen($main_content_buffer) > 10) {
                    // 智能处理：确保不输出不完整的URL标识
                    $safe_content = $this->getSafeContentForStreaming($push_content, $param->url_map ?? []);
                    $message_param->setContent($safe_content);
                    // 直接发送后续内容
                    FeiShuService::stream($message_param);
                    // 清空缓冲区
                    $main_content_buffer = '';
                    // 重置时间
                    $start_timestamp = microtime(true) * 1000;
                }

            }
        };
        $open_ai_param = new OpenAIParam($request_content);

        $result = (new OpenAI())->chatByStream($open_ai_param);


        // 兜底，如果没有消息id，直接返回所有内容
        if (!$chat_id_content && $buffer) {
            // 对buffer内容进行URL替换
            $processed_buffer = $this->replaceUrlMappings($buffer, $param->url_map ?? []);
            $message_param->setContent($processed_buffer);
        } else {
            // 个人对话才需要显示群来源
            if ($param->type === 'p2p') {
                // 获取群链接输出
                $chat_list = explode(',', $chat_id_content);
                // 过滤一下不存在的元素
                $chat_list = array_filter($chat_list, function ($chat_id) use ($param) {
                    return isset($param->chat_id_map[$chat_id]);
                });
                // 映射回来
                $chat_list = array_map(function ($chat_id) use ($param) {
                    return $param->chat_id_map[$chat_id];
                }, $chat_list);
                $chat_list = (new FeiShuAssistantBotChatModel())->getListByChatIdList($chat_list);
                $chat_model = new ChatsModel();
                $content = "\n\n";
                foreach ($chat_list as $chat_info) {
                    // 公共知识库
                    if ($chat_info->chat_id === self::COMMON_CHAT_ID) {
                        $chat_name = $chat_info->chat_name;
                        $content .= "来自：$chat_name\n";
                        continue;
                    }

                    // 个人知识库不显示名称 TODO 并不会进到这里 获取chat_info就拿不到了。 这里做个占位说明一下，看看到时候有需要再修改
                    if ($chat_info->chat_id == '0') {
                        continue;
                    }
                    $access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

                    // 获取一下群分享链接
                    $link_info = $chat_model->getChatLink($chat_info->chat_id, $access_token);
                    $chat_name = $chat_info->chat_name;

                    $content .= "来自群：[$chat_name]({$link_info['data']['share_link']})\n";

                }

                $push_content .= $content;
            }

            // 对最终内容进行URL替换
            $final_content = $this->replaceUrlMappings($push_content, $param->url_map ?? []);
            $message_param->setContent($final_content);
        }

        // 更新token 防止过期
        $access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $message_param->tenant_access_token = $access_token;
        // 直接发送后续内容
        FeiShuService::stream($message_param);


        $logger->info("用户提问: {$param->query}。大模型询问结束", [
            'chat_id'         => $param->chat_id,
            'union_id'        => $param->union_id,
            'push_content'    => $push_content,
            'result'          => $result,
            'chat_id_map'     => $param->chat_id_map,
            'url_map'         => $param->url_map,
            'request_content' => $request_content,
        ]);

//        // TODO 发送日志 特定人群
//        if ($param->receive_id_type === 'union_id' && in_array($param->union_id, ['on_ee55ca12cb03c11fc39c54e4aa705965'])) {
//            $result['request_content'] = $request_content;
//            $result['search'] = $param->respond;
//            $msg_id = 0;
//            $markdown = "```json\n";
//            $markdown .= json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
//            $markdown .= "\n```";
//            FeiShuService::streamOutput($msg_id, $access_token, $markdown, $param->receive_id, $param->receive_id_type);
//        }


        $this->addToSessionList($param->session_key, [
            [
                'role'    => 'user',
                'content' => $param->query
            ],
            [
                'role'    => 'assistant',
                'content' => $result['content'],
            ]
        ]);
    }


    public function getDocListByMessageId($message_id_list)
    {
        $doc_message_list = (new FeiShuMessageDocsModel())->docInMessage($message_id_list);
        $res_doc_list = $doc_message_list->unique(function ($item) {
            return $item->doc_type . $item->doc_token;
        });


        return [$res_doc_list->values(), $doc_message_list];

    }


    /**
     * QA的请求参数
     *
     * @param $query
     * @param $message_list
     * @param $session_key
     * @param $union_id
     * @return array
     * @throws Exception
     */
    private function getRequestParam($query, $message_list, $session_key, $union_id)
    {
        $system_content = GroupAssistantPrompt::QA;
        [$today, $day_of_week_cn] = (new DataBotService())->getCurrentDate();
        $username = FeiShuService::getNameByUnionId($union_id);
        $query = $username . ": " . $query;
        $system_content = str_replace(['{{message_list}}', '{{today}}', '{{day_of_week_cn}}', '{{username}}'], [$message_list, $today, $day_of_week_cn, $username], $system_content);
        $messages = [
            [
                "role"    => "system",
                "content" => $system_content,
            ]
        ];

        // 获取历史session 的 message
        $list = $this->getSessionList($session_key);
        foreach ($list as $item) {
            $messages[] = $item[0];
            if (isset($item[1]) && $item[1]) {
                $messages[] = $item[1];
            }
        }

        $messages[] = [
            "role"    => "user",
            "content" => $query,
        ];

        return [
            "messages"    => $messages,
            "temperature" => 0.5,
            "top_p"       => 0.9,
            'log'         => '知识问答',
        ];
    }


    /**
     * 获取改写的messages(向量检索用的)
     *
     * @param  $session_key
     * @return array
     * @throws RedisException
     * @throws Exception
     */
    private function getRewriteRequestParam($session_key)
    {
        $system_content = GroupAssistantPrompt::QA;
        [$today, $day_of_week_cn] = (new DataBotService())->getCurrentDate();
        $system_content = str_replace(['{{message_list}}'], ['省略....', $today, $day_of_week_cn], $system_content);
        $messages = [
            [
                "role"    => "system",
                "content" => $system_content,
            ]
        ];


        $list = $this->getSessionList($session_key);
        foreach ($list as $item) {
            $messages[] = $item[0];
            if (isset($item[1]) && $item[1]) {
                $messages[] = $item[1];
            }
        }

        // 最新问题不需要
//        $messages[] = [
//            "role"    => "user",
//            "content" => $query,
//        ];

        return $messages;
    }


    /**
     * 根据总结id获取消息id
     *
     * @param array $summary_id
     * @return array
     */
    private function getMsgIDListBySummaryID(array $summary_id)
    {
        $msg_id_list = [];
        $summary_data_list = (new FeishuMessageSummary())->getListByIds($summary_id);
        foreach ($summary_data_list as $summary_data) {
            $msg_id_list = array_merge($msg_id_list, json_decode($summary_data->msg_id_list, true));
        }
        return $msg_id_list;


    }

    private function getMessageList(KnowledgeParam $param, $summary_id_list)
    {
        // 拿到了id,去获取id对应的消息id列表
        $message_id_list = $this->getMsgIDListBySummaryID($summary_id_list);

        // 合并最近的消息,7天内50条
        $start_time = date('Y-m-d H:i:s', strtotime("-7 day"));
        $end_time = date('Y-m-d H:i:s');
        if ($param->type === 'group') {
            $new_message_id_list = (new FeiShuMessageModel())->getMessageIDByChatId($param->chat_list, $start_time, $end_time, 50);
        } else {
            $new_message_id_list = (new FeiShuMessageModel())->getListByUnionAndTime($param->union_id, $start_time, $end_time, 20)->pluck('message_id')->toArray();
        }
        $message_id_list = array_unique(array_merge($message_id_list, $new_message_id_list));

        $message_list = Collection::make();
        if ($message_id_list) {
            // 这里拿的消息要过滤@机器人的消息
            $message_list = (new FeiShuMessageModel())->getListByMessageIds($message_id_list, 0);
        }

        return [$message_list, $message_id_list];
    }

    /**
     * 出队 这里要把队列的所有元素拿出来
     *
     * @param $list_key
     * @return array
     * @throws RedisException
     */
    public function getSessionList($list_key)
    {
        $redis = RedisCache::getInstance();

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        foreach ($list as &$item) {
            $item = unserialize($item);
        }

        return $list;
    }

    /**
     * 历史记录入队列
     *
     * @param $list_key
     * @param $value
     * @return void
     * @throws RedisException
     */
    public function addToSessionList($list_key, $value)
    {
        $redis = RedisCache::getInstance();
        // 将元素推入列表的右侧

        $value = serialize($value);
        $redis->rPush($list_key, $value);

        // 设置列表的过期时间
        $redis->expire($list_key, self::SESSION_EXPIRE_TIME);

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        $length = 0;
        foreach ($list as &$item) {
            $item = unserialize($item);
            $length += strlen($item[0]['content']);
            // 检查列表长度是否超过最大值
            if ($length > self::SESSION_MAX_LENGTH_STR) {
                // 移除列表左侧的第一个元素
                $redis->lPop($list_key);
                break;
            }
        }
    }

    /**
     * 对分数进行时间加权重新计算，然后重排
     *
     * @param $chunk_list
     * @return array
     * @throws Exception
     */
    private function reRank($chunk_list)
    {
        $summary_id_list = [];
        $res_summary_id_list = [];
        foreach ($chunk_list as $type => $item_list) {
            foreach ($item_list as $item) {
                $summary_id_list[] = $item['summary_id'];
            }
        }
        // 获取总结的时间
        $summer_data_list = (new FeishuMessageSummary())->getListByIds($summary_id_list)->keyBy('id')->toArray();

        // 遍历三路召回的结果，去重新计算分数
        foreach ($chunk_list as $type => &$item_list) {
            foreach ($item_list as $index => &$item) {
                if (isset($summer_data_list[$item['summary_id']])) {
                    $target_date = date("Y-m-d", strtotime($summer_data_list[$item['summary_id']]->insert_time));
                    // 重新计算分数
                    $item['score'] = $this->calculateRankingScore($item['score'], $target_date);
                } else {
                    unset($item[$index]);
                }
            }

            // 重排，取前三个，按score降序排序
            usort($item_list, function ($a, $b) {
                return $b['score'] <=> $a['score'];
            });

            // 取前三个元素
            $item_list = array_slice($item_list, 0, 3);

            // 取summary_id存起来
            foreach ($item_list as $data) {
                $res_summary_id_list[] = $data['summary_id'];
            }
        }
        unset($item_list, $item);


        return $res_summary_id_list;
    }

    /**
     * 计算时间衰减后的排序分数
     * @param float $original_score 原始分数
     * @param string $target_date 目标日期（格式：Y-m-d）
     * @return float 调整后的分数
     * @throws Exception
     */
    public function calculateRankingScore(float $original_score, string $target_date)
    {
        // 创建日期对象
        $today = new DateTime('today');
        $target = new DateTime($target_date);

        // 计算天数差（仅处理过去日期）
        if ($target > $today) {
            return round($original_score, 4); // 未来日期不衰减
        }

        $interval = $today->diff($target);
        $days = $interval->days;

        // 应用衰减公式：分数 * 1.001^(-天数)
        $decay = pow(1.001, -$days);
        $final_score = $original_score * $decay;

        return round($final_score, 4);
    }

    /**
     * @param KnowledgeParam $param
     * @param $type
     * @return array
     * @throws Exception
     */
    protected function getDocFilter(KnowledgeParam $param, $type)
    {
        $chat_list = $param->chat_list;

        // 公共知识库
        $common_filter = [
            'op'    => "and",
            'conds' => [
                ['op' => 'must', 'field' => 'type', 'conds' => [$type]],
                ['op' => 'must', 'field' => 'chat_id', 'conds' => [self::COMMON_CHAT_ID]]
            ]
        ];


        // 日期范围有效判断（不超过180天）
        $is_valid_dateR_range = $param->start_date && $param->end_date
            && (strtotime($param->end_date) - strtotime($param->start_date) <= 86400 * 180);

        // 有效期范围内，则带上时间
        if ($is_valid_dateR_range) {
            $condition_list = [];
            $person_condition_list = [];
            // 拼接doc_id，id自带type，不需要type类型过滤
            foreach (Helpers::getDatesBetween($param->start_date, $param->end_date) as $date) {
                foreach ($chat_list as $chat_id) {
                    // 群聊知识库doc_id
                    $condition_list[] = "{$chat_id}_{$type}_{$date}";
                }
                // 个人知识库doc_id
                $person_condition_list[] = "{$param->union_id}_{$type}_{$date}";
            }
            // 群聊知识库
            $main_filter = ['op' => 'must', 'field' => 'doc_id', 'conds' => $condition_list];
            // 个人知识库
            $person_filter = ['op' => 'must', 'field' => 'doc_id', 'conds' => $person_condition_list];
        } else {
            $main_filter = [
                'op'    => "and",
                'conds' => [
                    ['op' => 'must', 'field' => 'type', 'conds' => [$type]],
                    ['op' => 'must', 'field' => 'chat_id', 'conds' => $chat_list]
                ]
            ];
            $person_filter = [
                'op'    => "and",
                'conds' => [
                    ['op' => 'must', 'field' => 'type', 'conds' => [$type]],
                    ['op' => 'must', 'field' => 'chat_id', 'conds' => [$param->union_id]]
                ]
            ];
        }

        // 合并公共知识库条件（仅私聊类型）
        if ($param->type === 'p2p') {
            // 判断一下群是否为空，空的话去掉main_filter
            $res_filter = $param->chat_list ? [$main_filter, $common_filter, $person_filter] : [$common_filter, $person_filter];
            return ['op' => 'or', 'conds' => $res_filter];
        } else {
            // 群聊就只有当前群的知识范围
            return $main_filter;
        }

    }
}