<?php
/**
 * 流式输出URL映射演示
 * 展示如何在流式输出中正确处理URL映射
 */

class StreamingUrlMappingDemo
{
    /**
     * 将大模型回答中的URL映射替换回真实URL
     */
    public function replaceUrlMappings($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            // 处理Markdown链接格式的替换
            if (!empty($url_info['text'])) {
                // 替换 [text](URL_X) 格式
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
            
            // 处理普通URL替换
            $content = str_replace($replacement_key, $url_info['url'], $content);
        }

        return $content;
    }

    /**
     * 模拟流式输出处理
     */
    public function simulateStreamingOutput()
    {
        echo "=== 流式输出URL映射演示 ===\n\n";

        // 模拟URL映射表
        $url_map = [
            'URL_1' => ['url' => 'https://api.example.com/docs', 'text' => 'API文档', 'original' => '[API文档](https://api.example.com/docs)'],
            'URL_2' => ['url' => 'https://github.com/company/project', 'text' => '', 'original' => 'https://github.com/company/project'],
            'URL_3' => ['url' => 'https://help.example.com/guide', 'text' => '', 'original' => 'https://help.example.com/guide']
        ];

        echo "URL映射表：\n";
        foreach ($url_map as $key => $info) {
            echo "  $key => {$info['url']}\n";
        }
        echo "\n";

        // 模拟大模型的流式输出chunks
        $chunks = [
            "根据您的问题，我找到了",
            "相关的资料：\n\n1. 请查看 ",
            "[API文档](URL_1) 了解",
            "详细的API使用方法\n2. 项目源码在 ",
            "URL_2 可以找到\n3. 更多",
            "帮助信息请访问 URL_3\n\n",
            "希望这些资源对您有帮助。"
        ];

        echo "=== 原有逻辑（错误的方式）===\n";
        $this->demonstrateWrongApproach($chunks, $url_map);

        echo "\n=== 修复后的逻辑（正确的方式）===\n";
        $this->demonstrateCorrectApproach($chunks, $url_map);
    }

    /**
     * 演示错误的处理方式（对整个累积内容进行替换）
     */
    private function demonstrateWrongApproach($chunks, $url_map)
    {
        echo "问题：每次都对整个累积内容进行URL替换\n\n";
        
        $push_content = '';
        $step = 1;
        
        foreach ($chunks as $chunk) {
            $push_content .= $chunk;
            
            // 错误的方式：每次都对整个内容进行替换
            $content_with_urls = $this->replaceUrlMappings($push_content, $url_map);
            
            echo "步骤 $step:\n";
            echo "  新chunk: '$chunk'\n";
            echo "  累积内容: '$push_content'\n";
            echo "  替换后: '$content_with_urls'\n";
            echo "  问题: " . $this->analyzeIssues($push_content, $content_with_urls) . "\n\n";
            
            $step++;
        }
    }

    /**
     * 演示正确的处理方式（只对新增chunk进行替换）
     */
    private function demonstrateCorrectApproach($chunks, $url_map)
    {
        echo "解决方案：只对新增的chunk进行URL替换，然后累积已处理的内容\n\n";
        
        $push_content = '';        // 原始累积内容（用于日志等）
        $processed_content = '';   // 已处理的累积内容（用于输出）
        $step = 1;
        
        foreach ($chunks as $chunk) {
            $push_content .= $chunk;
            
            // 正确的方式：只对新增的chunk进行替换
            $processed_chunk = $this->replaceUrlMappings($chunk, $url_map);
            $processed_content .= $processed_chunk;
            
            echo "步骤 $step:\n";
            echo "  新chunk: '$chunk'\n";
            echo "  处理后chunk: '$processed_chunk'\n";
            echo "  原始累积: '$push_content'\n";
            echo "  处理后累积: '$processed_content'\n";
            echo "  优势: 避免重复替换，性能更好，逻辑更清晰\n\n";
            
            $step++;
        }
        
        echo "最终输出给用户的内容：\n";
        echo "$processed_content\n\n";
    }

    /**
     * 分析错误方式的问题
     */
    private function analyzeIssues($original, $processed)
    {
        if ($original === $processed) {
            return "暂无问题";
        }
        
        $issues = [];
        
        // 检查是否有重复替换的迹象
        if (substr_count($processed, 'https://') > substr_count($original, 'URL_')) {
            $issues[] = "可能存在重复替换";
        }
        
        // 检查性能问题
        if (strlen($original) > 100) {
            $issues[] = "处理大量累积内容影响性能";
        }
        
        return empty($issues) ? "内容被正确替换" : implode(", ", $issues);
    }

    /**
     * 演示边界情况
     */
    public function demonstrateBoundaryConditions()
    {
        echo "=== 边界情况处理 ===\n\n";

        $url_map = [
            'URL_1' => ['url' => 'https://example.com', 'text' => '', 'original' => 'https://example.com']
        ];

        // 情况1：chunk中包含部分URL映射标识
        echo "情况1 - chunk中包含部分URL映射标识：\n";
        $partial_chunks = ["请访问 UR", "L_1 获取信息"];
        $processed_content = '';
        foreach ($partial_chunks as $i => $chunk) {
            $processed_chunk = $this->replaceUrlMappings($chunk, $url_map);
            $processed_content .= $processed_chunk;
            echo "  chunk" . ($i+1) . ": '$chunk' -> '$processed_chunk'\n";
        }
        echo "  最终结果: '$processed_content'\n";
        echo "  说明: 部分标识不会被替换，只有完整的URL_1才会被替换\n\n";

        // 情况2：空chunk
        echo "情况2 - 空chunk：\n";
        $empty_chunk = "";
        $processed_empty = $this->replaceUrlMappings($empty_chunk, $url_map);
        echo "  空chunk: '$empty_chunk' -> '$processed_empty'\n";
        echo "  说明: 空内容正常处理，不会出错\n\n";

        // 情况3：不包含URL的chunk
        echo "情况3 - 不包含URL的chunk：\n";
        $normal_chunk = "这是普通文本内容";
        $processed_normal = $this->replaceUrlMappings($normal_chunk, $url_map);
        echo "  普通chunk: '$normal_chunk' -> '$processed_normal'\n";
        echo "  说明: 普通内容保持不变\n\n";
    }
}

// 运行演示
if (php_sapi_name() === 'cli') {
    $demo = new StreamingUrlMappingDemo();
    $demo->simulateStreamingOutput();
    $demo->demonstrateBoundaryConditions();
    echo "演示完成！\n";
} else {
    // Web环境下的输出
    header('Content-Type: text/plain; charset=utf-8');
    $demo = new StreamingUrlMappingDemo();
    $demo->simulateStreamingOutput();
    $demo->demonstrateBoundaryConditions();
    echo "演示完成！\n";
}
