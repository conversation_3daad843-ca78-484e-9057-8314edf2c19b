<?php
/**
 * 智能流式输出测试
 * 测试真正解决URL标识分割问题的方案
 */

class SmartStreamingTest
{
    /**
     * 模拟URL替换方法
     */
    public function replaceUrlMappings($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            if (!empty($url_info['text'])) {
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
            $content = str_replace($replacement_key, $url_info['url'], $content);
        }

        return $content;
    }

    /**
     * 处理Markdown链接格式
     */
    public function processMarkdownLinks($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            if (!empty($url_info['text'])) {
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
        }

        return $content;
    }

    /**
     * 获取流式输出安全内容，避免输出不完整的URL标识
     */
    public function getSafeContentForStreaming($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        // 检查内容末尾是否有不完整的URL标识
        $safe_content = $content;
        
        // 检查是否以不完整的URL标识结尾
        if (preg_match('/URL_?\d*$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_start = $matches[0][1];
            $incomplete_part = $matches[0][0];
            
            // 如果是不完整的URL标识，暂时不输出这部分
            if (!isset($url_map[$incomplete_part])) {
                $safe_content = substr($content, 0, $match_start);
            }
        }
        
        // 检查是否有不完整的Markdown链接
        if (preg_match('/\[[^\]]*\]\(URL_?\d*$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $match_start = $matches[0][1];
            $safe_content = substr($content, 0, $match_start);
        }
        
        // 对安全内容进行URL替换
        $safe_content = $this->replaceUrlMappings($safe_content, $url_map);
        $safe_content = $this->processMarkdownLinks($safe_content, $url_map);
        
        return $safe_content;
    }

    /**
     * 测试智能流式输出
     */
    public function testSmartStreaming()
    {
        echo "=== 智能流式输出测试 ===\n\n";

        $url_map = [
            'URL_1' => ['url' => 'https://api.example.com/docs', 'text' => 'API文档', 'original' => '[API文档](https://api.example.com/docs)'],
            'URL_2' => ['url' => 'https://github.com/company/project', 'text' => '', 'original' => 'https://github.com/company/project']
        ];

        echo "URL映射表：\n";
        foreach ($url_map as $key => $info) {
            echo "  $key => {$info['url']}\n";
        }
        echo "\n";

        // 测试场景1：不完整的URL标识
        echo "=== 场景1：不完整的URL标识 ===\n";
        $test_cases_1 = [
            "请查看文档：URL_",
            "请查看文档：URL_1",
            "请查看文档：URL",
            "请查看文档：U",
            "请查看文档：UR"
        ];

        foreach ($test_cases_1 as $i => $content) {
            $safe_content = $this->getSafeContentForStreaming($content, $url_map);
            echo "测试 " . ($i + 1) . ":\n";
            echo "  原始内容: '$content'\n";
            echo "  安全输出: '$safe_content'\n";
            echo "  说明: " . $this->analyzeResult($content, $safe_content) . "\n\n";
        }

        // 测试场景2：不完整的Markdown链接
        echo "=== 场景2：不完整的Markdown链接 ===\n";
        $test_cases_2 = [
            "请参考 [API文档](URL_",
            "请参考 [API文档](URL_1)",
            "请参考 [API",
            "请参考 [API文档](",
            "请参考 [API文档](U"
        ];

        foreach ($test_cases_2 as $i => $content) {
            $safe_content = $this->getSafeContentForStreaming($content, $url_map);
            echo "测试 " . ($i + 1) . ":\n";
            echo "  原始内容: '$content'\n";
            echo "  安全输出: '$safe_content'\n";
            echo "  说明: " . $this->analyzeResult($content, $safe_content) . "\n\n";
        }

        // 测试场景3：完整内容
        echo "=== 场景3：完整内容 ===\n";
        $test_cases_3 = [
            "请查看 URL_1 和 URL_2",
            "参考 [API文档](URL_1) 了解详情",
            "普通文本没有URL"
        ];

        foreach ($test_cases_3 as $i => $content) {
            $safe_content = $this->getSafeContentForStreaming($content, $url_map);
            echo "测试 " . ($i + 1) . ":\n";
            echo "  原始内容: '$content'\n";
            echo "  安全输出: '$safe_content'\n";
            echo "  说明: " . $this->analyzeResult($content, $safe_content) . "\n\n";
        }
    }

    /**
     * 分析处理结果
     */
    private function analyzeResult($original, $processed)
    {
        if ($original === $processed) {
            return "内容完整，无需特殊处理";
        }
        
        if (strlen($processed) < strlen($original)) {
            return "检测到不完整URL标识，已暂时隐藏不完整部分";
        }
        
        if (strpos($processed, 'https://') !== false && strpos($original, 'URL_') !== false) {
            return "URL标识已成功替换为真实URL";
        }
        
        return "内容已处理";
    }

    /**
     * 模拟完整的流式输出过程
     */
    public function simulateCompleteStreamingProcess()
    {
        echo "=== 完整流式输出过程模拟 ===\n\n";

        $url_map = [
            'URL_1' => ['url' => 'https://api.example.com/docs', 'text' => '', 'original' => 'https://api.example.com/docs'],
            'URL_2' => ['url' => 'https://github.com/company/project', 'text' => '', 'original' => 'https://github.com/company/project']
        ];

        // 模拟被分割的chunks
        $chunks = [
            "请查看文档：UR",      // 不完整的URL开始
            "L_1 了解详情，",       // URL完成
            "项目地址：URL_",       // 新的不完整URL
            "2 获取代码"           // URL完成
        ];

        echo "模拟chunks: " . json_encode($chunks, JSON_UNESCAPED_UNICODE) . "\n\n";

        $push_content = '';
        $output_times = 0;

        foreach ($chunks as $i => $chunk) {
            $push_content .= $chunk;
            
            // 模拟每2个chunk输出一次
            if (($i + 1) % 2 == 0 || $i == count($chunks) - 1) {
                $output_times++;
                
                // 使用智能安全输出
                $safe_content = $this->getSafeContentForStreaming($push_content, $url_map);
                
                echo "输出 $output_times (chunk " . ($i + 1) . "):\n";
                echo "  累积内容: '$push_content'\n";
                echo "  用户看到: '$safe_content'\n";
                echo "  用户体验: " . $this->evaluateUserExperience($push_content, $safe_content) . "\n\n";
            }
        }

        echo "最终效果：用户在整个流式输出过程中都不会看到不完整的URL标识！\n\n";
    }

    /**
     * 评估用户体验
     */
    private function evaluateUserExperience($original, $safe)
    {
        if (preg_match('/URL_?\d*$/', $original) && !preg_match('/URL_?\d*$/', $safe)) {
            return "✅ 避免了显示不完整的URL标识";
        }
        
        if (strpos($safe, 'https://') !== false) {
            return "✅ 显示了完整的真实URL";
        }
        
        return "✅ 正常显示内容";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new SmartStreamingTest();
    $test->testSmartStreaming();
    $test->simulateCompleteStreamingProcess();
    echo "测试完成！\n";
} else {
    header('Content-Type: text/plain; charset=utf-8');
    $test = new SmartStreamingTest();
    $test->testSmartStreaming();
    $test->simulateCompleteStreamingProcess();
    echo "测试完成！\n";
}
