<?php
/**
 * URL映射功能演示
 * 展示如何防止大模型产生URL幻觉
 */

class UrlMappingDemo
{
    /**
     * 提取并替换消息中的URL，防止大模型产生URL幻觉
     */
    public function extractAndReplaceUrls($message_content, &$url_map, &$url_index)
    {
        // 匹配各种URL格式的正则表达式
        $url_patterns = [
            // HTTP/HTTPS URLs
            '/https?:\/\/[^\s\[\]()]+/i',
            // Markdown链接格式 [text](url)
            '/\[([^\]]*)\]\((https?:\/\/[^\s\[\]()]+)\)/i',
            // 飞书文档链接等特殊格式
            '/(?:https?:\/\/)?[a-zA-Z0-9][\w\-]*\.(?:feishu|lark)\.cn\/[^\s\[\]()]+/i'
        ];

        foreach ($url_patterns as $pattern) {
            $message_content = preg_replace_callback($pattern, function($matches) use (&$url_map, &$url_index) {
                $full_match = $matches[0];
                $url = '';
                $text = '';
                
                // 处理Markdown链接格式
                if (isset($matches[2])) {
                    $text = $matches[1];
                    $url = $matches[2];
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => $text, 'original' => $full_match];
                    $url_index++;
                    return "[{$text}]({$replacement_key})";
                } else {
                    // 处理普通URL
                    $url = $full_match;
                    $replacement_key = "URL_{$url_index}";
                    $url_map[$replacement_key] = ['url' => $url, 'text' => '', 'original' => $full_match];
                    $url_index++;
                    return $replacement_key;
                }
            }, $message_content);
        }

        return $message_content;
    }

    /**
     * 将大模型回答中的URL映射替换回真实URL
     */
    public function replaceUrlMappings($content, $url_map)
    {
        if (empty($url_map)) {
            return $content;
        }

        foreach ($url_map as $replacement_key => $url_info) {
            // 处理Markdown链接格式的替换
            if (!empty($url_info['text'])) {
                // 替换 [text](URL_X) 格式
                $pattern = '/\[' . preg_quote($url_info['text'], '/') . '\]\(' . preg_quote($replacement_key, '/') . '\)/';
                $replacement = "[{$url_info['text']}]({$url_info['url']})";
                $content = preg_replace($pattern, $replacement, $content);
            }
            
            // 处理普通URL替换
            $content = str_replace($replacement_key, $url_info['url'], $content);
        }

        return $content;
    }

    /**
     * 演示完整的URL映射流程
     */
    public function demonstrateUrlMapping()
    {
        echo "=== URL映射防幻觉演示 ===\n\n";

        // 模拟原始消息内容（包含真实URL）
        $originalMessage = "请参考以下文档：
1. [API文档](https://api.example.com/docs/v1) 
2. 项目地址：https://github.com/company/project
3. 飞书文档：https://company.feishu.cn/docs/doccnAbCdEfGhIjKlMnOpQrStUv
4. 更多信息请查看 https://help.example.com/guide";

        echo "1. 原始消息内容：\n";
        echo $originalMessage . "\n\n";

        // 步骤1：提取URL并替换为映射标识
        $url_map = [];
        $url_index = 1;
        $processedMessage = $this->extractAndReplaceUrls($originalMessage, $url_map, $url_index);

        echo "2. 处理后发送给大模型的内容（URL已被映射）：\n";
        echo $processedMessage . "\n\n";

        echo "3. URL映射表：\n";
        foreach ($url_map as $key => $info) {
            echo "  $key => {$info['url']}" . (!empty($info['text']) ? " (文本: {$info['text']})" : "") . "\n";
        }
        echo "\n";

        // 模拟大模型的回答（可能包含URL_X格式的引用）
        $aiResponse = "根据您提供的资料，我找到了相关信息：

1. 关于API的使用方法，请查看 [API文档](URL_1)
2. 项目的源代码可以在 URL_2 找到
3. 详细的操作指南在飞书文档 URL_3 中有说明
4. 如需更多帮助，建议查看 URL_4

这些资源应该能够解答您的问题。";

        echo "4. 大模型的回答（包含URL映射标识）：\n";
        echo $aiResponse . "\n\n";

        // 步骤2：将URL映射标识替换回真实URL
        $finalResponse = $this->replaceUrlMappings($aiResponse, $url_map);

        echo "5. 最终返回给用户的内容（URL已还原）：\n";
        echo $finalResponse . "\n\n";

        echo "=== 防幻觉效果说明 ===\n";
        echo "✅ 大模型只能看到 URL_1, URL_2 等映射标识，无法编造真实URL\n";
        echo "✅ 系统确保所有返回的URL都来自原始参考资料\n";
        echo "✅ 用户最终看到的是完整、准确的真实URL\n";
        echo "✅ 有效防止了URL幻觉问题\n\n";
    }

    /**
     * 演示边界情况处理
     */
    public function demonstrateEdgeCases()
    {
        echo "=== 边界情况演示 ===\n\n";

        // 情况1：大模型尝试编造URL（但因为没有映射会保持原样）
        $fakeUrlResponse = "您可以访问 https://fake-url.com/nonexistent 获取更多信息。";
        $url_map = [
            'URL_1' => ['url' => 'https://real-url.com/docs', 'text' => '', 'original' => 'https://real-url.com/docs']
        ];

        echo "情况1 - 大模型尝试编造URL：\n";
        echo "大模型回答: $fakeUrlResponse\n";
        $result1 = $this->replaceUrlMappings($fakeUrlResponse, $url_map);
        echo "处理结果: $result1\n";
        echo "说明: 编造的URL不会被替换，保持原样，便于识别和处理\n\n";

        // 情况2：正确使用映射标识
        $correctResponse = "请查看 URL_1 了解详情。";
        echo "情况2 - 正确使用映射标识：\n";
        echo "大模型回答: $correctResponse\n";
        $result2 = $this->replaceUrlMappings($correctResponse, $url_map);
        echo "处理结果: $result2\n";
        echo "说明: 映射标识被正确替换为真实URL\n\n";
    }
}

// 运行演示
if (php_sapi_name() === 'cli') {
    $demo = new UrlMappingDemo();
    $demo->demonstrateUrlMapping();
    $demo->demonstrateEdgeCases();
    echo "演示完成！\n";
} else {
    // Web环境下的输出
    header('Content-Type: text/plain; charset=utf-8');
    $demo = new UrlMappingDemo();
    $demo->demonstrateUrlMapping();
    $demo->demonstrateEdgeCases();
    echo "演示完成！\n";
}
